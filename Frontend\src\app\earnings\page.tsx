'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import Link from 'next/link';

// 收益数据接口
interface EarningsData {
  totalEarnings: number;
  thisMonthEarnings: number;
  lastMonthEarnings: number;
  pendingEarnings: number;
  availableForWithdraw: number;
  totalLikes: number;
  revenueStreams: {
    contentViews: number;
    premiumSubscriptions: number;
    donations: number;
    sponsorships: number;
    merchandise: number;
    adRevenue: number;
    affiliateCommissions: number;
  };
  platformShares: {
    [key: string]: { creatorShare: number; platformShare: number };
  };
  viewMetrics: {
    totalViews: number;
    uniqueViewers: number;
    avgViewDuration: number;
    bounceRate: number;
    adImpressions: number;
    adClickRate: number;
    revenuePerView: number;
  };
  monthlyBreakdown: Array<{
    month: string;
    earnings: number;
    views: number;
    engagement: number;
  }>;
  topPerformingContent: Array<{
    id: number;
    title: string;
    type: 'article' | 'video' | 'audio';
    earnings: number;
    views: number;
    publishedAt: string;
  }>;
  fraudDetection: {
    riskScore: number;
    suspiciousActivities: string[];
    verificationStatus: 'verified' | 'pending' | 'flagged';
    qualityScore: number;
  };
  aiRecommendations: {
    contentSuggestions: string[];
    optimizationTips: string[];
    trendingTopics: string[];
  };
}

export default function EarningsPage() {
  const router = useRouter();
  const { isAuthenticated, user, isLoading } = useSimpleAuth();
  const [earningsData, setEarningsData] = useState<EarningsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');


  // 认证检查和数据加载
  useEffect(() => {
    // 简化认证检查逻辑
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    if (!isLoading && isAuthenticated && !earningsData) {
      // 直接加载数据，不需要复杂的超时处理
      loadEarningsData();
    }
  }, [isAuthenticated, isLoading, router, earningsData]);

  const loadEarningsData = async () => {
    try {
      setLoading(true);

      // 模拟数据
      const mockEarningsData: EarningsData = {
        totalEarnings: 2847.50,
        thisMonthEarnings: 456.30,
        lastMonthEarnings: 398.20,
        pendingEarnings: 123.45,
        availableForWithdraw: 2724.05,
        totalLikes: 3420,
        revenueStreams: {
          contentViews: 1234.50, // 70% creator share from ad revenue
          premiumSubscriptions: 890.00, // 80% creator share
          donations: 345.60, // 95% creator share (5% platform fee)
          sponsorships: 267.40, // 85% creator share
          merchandise: 110.00, // 75% creator share
          adRevenue: 456.80, // New: Direct ad placement revenue
          affiliateCommissions: 123.20 // New: Affiliate marketing revenue
        },
        platformShares: {
          contentViews: { creatorShare: 70, platformShare: 30 },
          premiumSubscriptions: { creatorShare: 80, platformShare: 20 },
          donations: { creatorShare: 95, platformShare: 5 },
          sponsorships: { creatorShare: 85, platformShare: 15 },
          merchandise: { creatorShare: 75, platformShare: 25 },
          adRevenue: { creatorShare: 65, platformShare: 35 },
          affiliateCommissions: { creatorShare: 90, platformShare: 10 }
        },
        viewMetrics: {
          totalViews: 45230,
          uniqueViewers: 32180,
          avgViewDuration: 4.2, // minutes
          bounceRate: 23.5, // percentage
          adImpressions: 89450,
          adClickRate: 2.8, // percentage
          revenuePerView: 0.027 // dollars
        },
        monthlyBreakdown: [
          { month: 'Jan 2024', earnings: 234.50, views: 3200, engagement: 85 },
          { month: 'Feb 2024', earnings: 298.30, views: 4100, engagement: 92 },
          { month: 'Mar 2024', earnings: 356.80, views: 4800, engagement: 88 },
          { month: 'Apr 2024', earnings: 398.20, views: 5200, engagement: 94 },
          { month: 'May 2024', earnings: 456.30, views: 5800, engagement: 96 }
        ],
        topPerformingContent: [
          {
            id: 1,
            title: 'The Future of Artificial Intelligence in 2024',
            type: 'article',
            earnings: 89.50,
            views: 2340,
            publishedAt: '2024-05-15'
          },
          {
            id: 3,
            title: 'Building a Modern Web Application with Next.js 14',
            type: 'video',
            earnings: 76.20,
            views: 1890,
            publishedAt: '2024-05-10'
          },
          {
            id: 6,
            title: 'The Psychology of Success: A Deep Dive',
            type: 'audio',
            earnings: 65.80,
            views: 1560,
            publishedAt: '2024-05-08'
          }
        ],
        fraudDetection: {
          riskScore: 15,
          suspiciousActivities: [],
          verificationStatus: 'verified',
          qualityScore: 92
        },
        aiRecommendations: {
          contentSuggestions: [
            '基于您的AI技术文章表现，建议创作更多机器学习相关内容',
            '您的视频教程很受欢迎，可以考虑制作系列课程',
            '心理学话题有潜力，建议深入探索认知科学领域'
          ],
          optimizationTips: [
            '提高视频缩略图质量可增加20%点击率',
            '在文章开头添加目录可提升用户停留时间',
            '使用更多互动元素可提高评论参与度'
          ],
          trendingTopics: [
            'AI工具实战应用',
            '远程工作效率提升',
            '个人品牌建设'
          ]
        }
      };

      // 模拟加载延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      setEarningsData(mockEarningsData);
      setLoading(false);
    } catch (error) {
      console.error('Error loading earnings data:', error);
      setLoading(false);
    }
  };

  // 如果认证状态还在加载中，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // 如果未认证，显示登录提示
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Earnings Dashboard</h2>
          <p className="text-gray-600 mb-6">Please log in to view your earnings</p>
          <Link
            href="/auth/login"
            className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Login Now
          </Link>
        </div>
      </div>
    );
  }

  if (loading || !earningsData) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  const growthPercentage = earningsData.lastMonthEarnings > 0 
    ? ((earningsData.thisMonthEarnings - earningsData.lastMonthEarnings) / earningsData.lastMonthEarnings) * 100 
    : 0;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">💰 Earnings Dashboard</h1>
          <p className="text-gray-600">Track your content monetization and revenue streams</p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                <p className="text-2xl font-bold text-gray-900">${earningsData.totalEarnings.toFixed(2)}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 text-xl">💵</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">This Month</p>
                <p className="text-2xl font-bold text-gray-900">${earningsData.thisMonthEarnings.toFixed(2)}</p>
                <p className={`text-sm ${growthPercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {growthPercentage >= 0 ? '↗️' : '↘️'} {Math.abs(growthPercentage).toFixed(1)}% vs last month
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 text-xl">📈</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-gray-900">${earningsData.pendingEarnings.toFixed(2)}</p>
                <p className="text-sm text-gray-500">Processing...</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 text-xl">⏳</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Available</p>
                <p className="text-2xl font-bold text-gray-900">${earningsData.availableForWithdraw.toFixed(2)}</p>
                <Link
                  href="/withdraw"
                  className="mt-2 inline-block text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors"
                >
                  Withdraw →
                </Link>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 text-xl">💳</span>
              </div>
            </div>
          </div>
        </div>

        {/* View Metrics & Ad Performance */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">View Analytics</h3>
              <span className="text-2xl">📊</span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Views</span>
                <span className="font-bold">{earningsData.viewMetrics.totalViews.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Unique Viewers</span>
                <span className="font-bold">{earningsData.viewMetrics.uniqueViewers.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Avg Duration</span>
                <span className="font-bold">{earningsData.viewMetrics.avgViewDuration}m</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Revenue/View</span>
                <span className="font-bold text-green-600">${earningsData.viewMetrics.revenuePerView.toFixed(3)}</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Ad Performance</h3>
              <span className="text-2xl">📢</span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Ad Impressions</span>
                <span className="font-bold">{earningsData.viewMetrics.adImpressions.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Click Rate</span>
                <span className="font-bold text-blue-600">{earningsData.viewMetrics.adClickRate}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Bounce Rate</span>
                <span className="font-bold text-orange-600">{earningsData.viewMetrics.bounceRate}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Ad Revenue</span>
                <span className="font-bold text-green-600">${earningsData.revenueStreams.adRevenue.toFixed(2)}</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Revenue Share</h3>
              <span className="text-2xl">🤝</span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Your Share</span>
                <span className="font-bold text-green-600">70-95%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Platform Fee</span>
                <span className="font-bold text-gray-600">5-30%</span>
              </div>
              <div className="text-xs text-gray-500 mt-2">
                Varies by revenue type. Higher engagement = better rates.
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Enhanced Revenue Streams */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Streams & Share Rates</h3>
            <div className="space-y-4">
              <div className="p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-blue-500">👁️</span>
                    <span className="font-medium">Content Views</span>
                  </div>
                  <span className="font-bold text-gray-900">${earningsData.revenueStreams.contentViews.toFixed(2)}</span>
                </div>
                <div className="text-xs text-blue-700">Your share: 70% • Platform: 30%</div>
              </div>
              
              <div className="p-3 bg-gradient-to-r from-red-50 to-red-100 rounded-lg border border-red-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-red-500">📢</span>
                    <span className="font-medium">Ad Revenue</span>
                  </div>
                  <span className="font-bold text-gray-900">${earningsData.revenueStreams.adRevenue.toFixed(2)}</span>
                </div>
                <div className="text-xs text-red-700">Your share: 65% • Platform: 35%</div>
              </div>
              
              <div className="p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-purple-500">⭐</span>
                    <span className="font-medium">Premium Subscriptions</span>
                  </div>
                  <span className="font-bold text-gray-900">${earningsData.revenueStreams.premiumSubscriptions.toFixed(2)}</span>
                </div>
                <div className="text-xs text-purple-700">Your share: 80% • Platform: 20%</div>
              </div>
              
              <div className="p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-green-500">❤️</span>
                    <span className="font-medium">Donations</span>
                  </div>
                  <span className="font-bold text-gray-900">${earningsData.revenueStreams.donations.toFixed(2)}</span>
                </div>
                <div className="text-xs text-green-700">Your share: 95% • Platform: 5%</div>
              </div>
              
              <div className="p-3 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg border border-yellow-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-yellow-600">🤝</span>
                    <span className="font-medium">Sponsorships</span>
                  </div>
                  <span className="font-bold text-gray-900">${earningsData.revenueStreams.sponsorships.toFixed(2)}</span>
                </div>
                <div className="text-xs text-yellow-700">Your share: 85% • Platform: 15%</div>
              </div>
              
              <div className="p-3 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg border border-orange-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-orange-500">🛒</span>
                    <span className="font-medium">Affiliate Commissions</span>
                  </div>
                  <span className="font-bold text-gray-900">${earningsData.revenueStreams.affiliateCommissions.toFixed(2)}</span>
                </div>
                <div className="text-xs text-orange-700">Your share: 90% • Platform: 10%</div>
              </div>
            </div>
          </div>

          {/* Security Detection */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">🛡️ Account Security Check</h3>
            <div className="space-y-4">
              <div className={`p-4 rounded-lg border ${
                earningsData.fraudDetection.riskScore < 30 
                  ? 'bg-green-50 border-green-200' 
                  : earningsData.fraudDetection.riskScore < 70 
                  ? 'bg-yellow-50 border-yellow-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center justify-between mb-2">
                  <h4 className={`font-medium ${
                    earningsData.fraudDetection.riskScore < 30 
                      ? 'text-green-800' 
                      : earningsData.fraudDetection.riskScore < 70 
                      ? 'text-yellow-800' 
                      : 'text-red-800'
                  }`}>Risk Score</h4>
                  <span className={`text-2xl font-bold ${
                    earningsData.fraudDetection.riskScore < 30 
                      ? 'text-green-600' 
                      : earningsData.fraudDetection.riskScore < 70 
                      ? 'text-yellow-600' 
                      : 'text-red-600'
                  }`}>{earningsData.fraudDetection.riskScore}/100</span>
                </div>
                <p className={`text-sm ${
                  earningsData.fraudDetection.riskScore < 30 
                    ? 'text-green-700' 
                    : earningsData.fraudDetection.riskScore < 70 
                    ? 'text-yellow-700' 
                    : 'text-red-700'
                }`}>
                  {earningsData.fraudDetection.riskScore < 30 
                    ? 'Account status is good, no abnormal activity detected' 
                    : earningsData.fraudDetection.riskScore < 70 
                    ? 'Minor anomalies detected, please monitor account activity' 
                    : 'High-risk activity detected, please contact customer service'}
                </p>
              </div>
              
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-800 mb-2">Content Quality Score</h4>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-blue-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${earningsData.fraudDetection.qualityScore}%` }}
                    ></div>
                  </div>
                  <span className="text-blue-800 font-bold">{earningsData.fraudDetection.qualityScore}/100</span>
                </div>
                <p className="text-sm text-blue-700 mt-1">High-quality content gets better recommendations and revenue sharing</p>
              </div>
            </div>
          </div>

          {/* AI Recommendation System */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">🤖 AI Smart Recommendations</h3>
            <div className="space-y-4">
              <div className="p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg border border-purple-200">
                <h4 className="font-medium text-purple-800 mb-2">📝 Content Suggestions</h4>
                <ul className="text-sm text-purple-700 space-y-1">
                  <li>• Based on your AI technology articles, consider creating more machine learning content</li>
                  <li>• Your video tutorials are popular, consider creating series courses</li>
                  <li>• Psychology topics have potential, explore cognitive science field</li>
                </ul>
              </div>
              
              <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                <h4 className="font-medium text-green-800 mb-2">💡 Optimization Tips</h4>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• Improving video thumbnail quality can increase click rate by 20%</li>
                  <li>• Adding table of contents at article beginning improves user retention</li>
                  <li>• Using more interactive elements can boost comment engagement</li>
                </ul>
              </div>
              
              <div className="p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200">
                <h4 className="font-medium text-orange-800 mb-2">🔥 Trending Topics</h4>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-orange-100 text-orange-800 text-sm rounded-full border border-orange-300">
                    AI Tools & Applications
                  </span>
                  <span className="px-3 py-1 bg-orange-100 text-orange-800 text-sm rounded-full border border-orange-300">
                    Remote Work Productivity
                  </span>
                  <span className="px-3 py-1 bg-orange-100 text-orange-800 text-sm rounded-full border border-orange-300">
                    Personal Branding
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Top Performing Content */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Content</h3>
            <div className="space-y-4">
              {earningsData.topPerformingContent.map((content, index) => (
                <button
                  key={content.id}
                  onClick={() => {
                    const routeMap = {
                      article: `/article/${content.id}`,
                      video: `/video/${content.id}`,
                      audio: `/audio/${content.id}`
                    };
                    router.push(routeMap[content.type]);
                  }}
                  className="w-full flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer text-left"
                >
                  <div className="flex items-center space-x-3">
                    <span className="w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                      {index + 1}
                    </span>
                    <div>
                      <p className="font-medium text-gray-900">{content.title}</p>
                      <p className="text-sm text-gray-500">
                        {content.type} • {content.views.toLocaleString()} views
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-bold text-green-600">${content.earnings.toFixed(2)}</span>
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Monthly Breakdown */}
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Performance</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Month</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Earnings</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Views</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Engagement</th>
                </tr>
              </thead>
              <tbody>
                {earningsData.monthlyBreakdown.map((month) => (
                  <tr key={month.month} className="border-b border-gray-100">
                    <td className="py-3 px-4 text-gray-900">{month.month}</td>
                    <td className="py-3 px-4 font-medium text-green-600">${month.earnings.toFixed(2)}</td>
                    <td className="py-3 px-4 text-gray-600">{month.views.toLocaleString()}</td>
                    <td className="py-3 px-4 text-gray-600">{month.engagement}%</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
