'use client';

import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, LightBulbIcon } from '@heroicons/react/24/outline';

interface QuickTipsProps {
  className?: string;
  contentType?: 'article' | 'video' | 'audio' | 'live';
}

interface Tip {
  id: number;
  title: string;
  description: string;
  detailedDescription?: string;
  icon: string;
  category: string[];
}

const tips: Tip[] = [
  {
    id: 1,
    title: 'Write Engaging Headlines',
    description: 'Use numbers, questions, or power words to grab attention',
    detailedDescription: 'Effective headlines should be specific, create curiosity, and promise value. Try using numbers (e.g., "5 Ways to..."), questions (e.g., "How to...?"), or power words like "Ultimate", "Essential", "Proven". Keep it under 60 characters for better SEO.',
    icon: '✍️',
    category: ['article', 'video', 'audio', 'live']
  },
  {
    id: 2,
    title: 'Add Rich Media',
    description: 'Include images, videos, or audio to enhance your content',
    detailedDescription: 'Visual content increases engagement by 94%. Use high-quality images, infographics, or videos to break up text and illustrate your points. Ensure all media is optimized for web and mobile viewing.',
    icon: '🎬',
    category: ['article', 'video']
  },
  {
    id: 3,
    title: 'Use Proper Formatting',
    description: 'Break up text with headings, lists, and paragraphs',
    detailedDescription: 'Good formatting improves readability and SEO. Use H2 and H3 headings to structure content, bullet points for lists, and keep paragraphs to 2-3 sentences. White space is your friend.',
    icon: '📝',
    category: ['article']
  },
  {
    id: 4,
    title: 'Engage Your Audience',
    description: 'Ask questions and encourage comments from readers',
    detailedDescription: 'End your content with thought-provoking questions or calls-to-action. Respond to comments promptly to build community. Consider creating polls or asking for opinions to boost engagement.',
    icon: '💬',
    category: ['article', 'video', 'audio', 'live']
  },
  {
    id: 5,
    title: 'Optimize Video Quality',
    description: 'Use good lighting and clear audio for professional results',
    detailedDescription: 'Good lighting is more important than expensive cameras. Film near windows for natural light or use softbox lights. Invest in a quality microphone - audio quality is crucial for viewer retention.',
    icon: '🎥',
    category: ['video', 'live']
  },
  {
    id: 6,
    title: 'Audio Clarity',
    description: 'Record in quiet environments with quality equipment',
    detailedDescription: 'Use a dedicated microphone and record in a quiet room with soft furnishings to reduce echo. Consider using noise reduction software and always do a test recording first.',
    icon: '🎙️',
    category: ['audio', 'live']
  },
  {
    id: 7,
    title: 'Live Stream Preparation',
    description: 'Test your setup and prepare talking points beforehand',
    detailedDescription: 'Test your internet connection, camera, and microphone before going live. Prepare an outline of topics to discuss, have backup content ready, and inform your audience about the stream schedule.',
    icon: '📡',
    category: ['live']
  }
];

const QuickTips: React.FC<QuickTipsProps> = ({ className = '', contentType = 'article' }) => {
  const [expandedTip, setExpandedTip] = useState<number | null>(null);

  // Filter tips based on content type
  const filteredTips = tips.filter(tip => tip.category.includes(contentType));

  const toggleExpanded = (tipId: number) => {
    setExpandedTip(expandedTip === tipId ? null : tipId);
  };

  return (
    <div className={`bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden ${className}`}>
      {/* Header - 固定显示，不可折叠 */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <h3 className="text-base font-semibold text-gray-800 flex items-center">
          <LightBulbIcon className="w-5 h-5 mr-2 text-blue-500" />
          Quick Tips
          <span className="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
            {filteredTips.length}
          </span>
        </h3>
      </div>

      {/* Tips content - 始终显示 */}
      <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
        {filteredTips.map((tip) => (
          <div key={tip.id} className="border border-gray-100 rounded-lg overflow-hidden">
            {/* 可点击的文章标题区域 */}
            <button
              onClick={() => toggleExpanded(tip.id)}
              className="w-full flex items-start space-x-3 p-3 hover:bg-gray-50 transition-colors text-left"
            >
              <span className="text-lg flex-shrink-0">{tip.icon}</span>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-800 truncate">{tip.title}</h4>
                  <div className="ml-2 flex-shrink-0">
                    {expandedTip === tip.id ? (
                      <ChevronUpIcon className="w-4 h-4 text-gray-500" />
                    ) : (
                      <ChevronDownIcon className="w-4 h-4 text-gray-500" />
                    )}
                  </div>
                </div>
                <p className="text-xs text-gray-600 mt-1">{tip.description}</p>
              </div>
            </button>

            {/* 点击展开的详细说明 */}
            {expandedTip === tip.id && tip.detailedDescription && (
              <div className="px-3 pb-3 border-t border-gray-100 bg-blue-50/50">
                <div className="pt-3 pl-8">
                  <p className="text-xs text-gray-700 leading-relaxed">
                    {tip.detailedDescription}
                  </p>
                </div>
              </div>
            )}
          </div>
        ))}

        {filteredTips.length === 0 && (
          <div className="text-center py-6 text-gray-500">
            <LightBulbIcon className="w-8 h-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No tips available for this content type</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuickTips;