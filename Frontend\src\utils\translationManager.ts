/**
 * 翻译管理器
 * PC端：使用浏览器自动翻译
 * 移动端：使用系统翻译
 * 遵循项目规则：TypeScript强类型、完整错误处理、设备适配
 */

import { 
  getTranslationStrategy, 
  isDesktopDevice, 
  isMobileDevice,
  getBrowserType,
  getOperatingSystem 
} from './deviceDetection';

export type TranslationStrategy = 'browser' | 'system' | 'none';

export interface TranslationConfig {
  strategy: TranslationStrategy;
  enabled: boolean;
  autoDetect: boolean;
  defaultLanguage: string;
  supportedLanguages: string[];
}

/**
 * 翻译管理器类
 */
export class TranslationManager {
  private config: TranslationConfig;
  private initialized: boolean = false;

  constructor() {
    this.config = {
      strategy: 'none',
      enabled: false,
      autoDetect: true,
      defaultLanguage: 'en',
      supportedLanguages: ['en', 'zh-CN', 'zh-TW', 'ja', 'ko', 'es', 'fr', 'de', 'ru']
    };
  }

  /**
   * 初始化翻译管理器
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 检测设备和翻译策略
      const strategy = getTranslationStrategy();
      this.config.strategy = strategy;
      this.config.enabled = strategy !== 'none';

      console.log(`Translation strategy: ${strategy}`);

      // 根据策略初始化相应的翻译功能
      switch (strategy) {
        case 'browser':
          await this.initializeBrowserTranslation();
          break;
        case 'system':
          await this.initializeSystemTranslation();
          break;
        default:
          console.log('No translation strategy available');
      }

      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize translation manager:', error);
    }
  }

  /**
   * 初始化浏览器翻译（PC端）
   */
  private async initializeBrowserTranslation(): Promise<void> {
    if (typeof window === 'undefined') return;

    const browser = getBrowserType();
    
    console.log(`Initializing browser translation for ${browser}`);

    // 为页面添加翻译提示
    this.addBrowserTranslationHints();

    // 检测页面语言并设置适当的lang属性
    this.setupLanguageDetection();
  }

  /**
   * 初始化系统翻译（移动端）
   */
  private async initializeSystemTranslation(): Promise<void> {
    if (typeof window === 'undefined') return;

    const os = getOperatingSystem();
    
    console.log(`Initializing system translation for ${os}`);

    // 添加系统翻译提示
    this.addSystemTranslationHints();

    // 设置页面元数据以支持系统翻译
    this.setupSystemTranslationMeta();
  }

  /**
   * 添加浏览器翻译提示
   */
  private addBrowserTranslationHints(): void {
    // 设置页面语言属性
    document.documentElement.lang = this.config.defaultLanguage;

    // 添加翻译相关的meta标签
    const metaTags = [
      { name: 'google', content: 'translate' },
      { name: 'google-translate-customization', content: 'auto' }
    ];

    metaTags.forEach(({ name, content }) => {
      let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
      if (!meta) {
        meta = document.createElement('meta');
        meta.name = name;
        document.head.appendChild(meta);
      }
      meta.content = content;
    });

    // 添加翻译提示样式
    this.addTranslationStyles();
  }

  /**
   * 添加系统翻译提示
   */
  private addSystemTranslationHints(): void {
    // 设置页面语言属性
    document.documentElement.lang = this.config.defaultLanguage;

    // 添加系统翻译相关的meta标签
    const metaTags = [
      { name: 'apple-mobile-web-app-capable', content: 'yes' },
      { name: 'mobile-web-app-capable', content: 'yes' },
      { name: 'format-detection', content: 'telephone=no' }
    ];

    metaTags.forEach(({ name, content }) => {
      let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
      if (!meta) {
        meta = document.createElement('meta');
        meta.name = name;
        document.head.appendChild(meta);
      }
      meta.content = content;
    });
  }

  /**
   * 设置语言检测
   */
  private setupLanguageDetection(): void {
    if (typeof window === 'undefined') return;

    // 检测用户首选语言
    const userLanguage = navigator.language || navigator.languages?.[0] || this.config.defaultLanguage;
    
    // 如果用户语言不是英文，添加翻译提示
    if (!userLanguage.startsWith('en')) {
      this.showTranslationTip(userLanguage);
    }
  }

  /**
   * 设置系统翻译元数据
   */
  private setupSystemTranslationMeta(): void {
    // 添加结构化数据以帮助系统翻译
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "inLanguage": this.config.defaultLanguage,
      "name": "Newzora",
      "description": "Social media platform"
    };

    let script = document.querySelector('script[type="application/ld+json"]');
    if (!script) {
      script = document.createElement('script');
      script.type = 'application/ld+json';
      document.head.appendChild(script);
    }
    script.textContent = JSON.stringify(structuredData);
  }

  /**
   * 显示翻译提示
   */
  private showTranslationTip(userLanguage: string): void {
    const strategy = this.config.strategy;
    
    if (strategy === 'browser') {
      this.showBrowserTranslationTip(userLanguage);
    } else if (strategy === 'system') {
      this.showSystemTranslationTip(userLanguage);
    }
  }

  /**
   * 显示浏览器翻译提示
   */
  private showBrowserTranslationTip(userLanguage: string): void {
    const browser = getBrowserType();
    let tipText = '';

    switch (browser) {
      case 'chrome':
        tipText = 'Right-click and select "Translate to ' + userLanguage + '" to translate this page.';
        break;
      case 'edge':
        tipText = 'Click the translate icon in the address bar to translate this page.';
        break;
      case 'firefox':
        tipText = 'Use Firefox Translate add-on to translate this page.';
        break;
      default:
        tipText = 'Use your browser\'s translation feature to translate this page.';
    }

    console.log('Browser translation tip:', tipText);
  }

  /**
   * 显示系统翻译提示
   */
  private showSystemTranslationTip(userLanguage: string): void {
    const os = getOperatingSystem();
    let tipText = '';

    switch (os) {
      case 'ios':
        tipText = 'Use Safari\'s built-in translation or iOS system translation features.';
        break;
      case 'android':
        tipText = 'Use Google Translate app or Chrome\'s translation feature.';
        break;
      default:
        tipText = 'Use your device\'s translation features to translate this page.';
    }

    console.log('System translation tip:', tipText);
  }

  /**
   * 添加翻译样式
   */
  private addTranslationStyles(): void {
    const styleId = 'translation-styles';
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      /* 翻译提示样式 */
      .translation-tip {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 14px;
        color: #374151;
        z-index: 9999;
        max-width: 300px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .translation-tip.mobile {
        position: fixed;
        bottom: 20px;
        left: 20px;
        right: 20px;
        top: auto;
        max-width: none;
      }

      /* 隐藏Google翻译工具栏的样式冲突 */
      .goog-te-banner-frame {
        display: none !important;
      }
      
      body {
        top: 0 !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 获取当前配置
   */
  public getConfig(): TranslationConfig {
    return { ...this.config };
  }

  /**
   * 检查是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 清理翻译管理器
   */
  public cleanup(): void {
    // 移除添加的样式和元素
    const styleElement = document.getElementById('translation-styles');
    if (styleElement) {
      styleElement.remove();
    }

    this.initialized = false;
  }
}

// 创建全局翻译管理器实例
export const translationManager = new TranslationManager();
