# 🗄️ Newzora 数据库配置说明

## 📋 概述

Newzora 项目现在使用两个独立的 PostgreSQL 数据库，分别服务于主站前台和后台管理系统，实现数据隔离和安全管理。

## 🏗️ 数据库架构

### 主站前台数据库
- **数据库名称**: `PostgreSQL-newzora_web`
- **用户名**: `postgres`
- **密码**: `wasd080980!`
- **端口**: `5432`
- **用途**: 存储用户数据、文章内容、评论、社交功能等前台业务数据

### 后台管理系统数据库
- **数据库名称**: `PostgreSQL-newzora_admin`
- **用户名**: `postgres`
- **密码**: `QWasd080980!`
- **端口**: `5433` (Docker环境)
- **用途**: 存储管理员账户、系统设置、审核记录等后台管理数据

## 🔧 配置文件更新

### 主站前台配置
- **环境变量文件**: `Backend/.env`
- **数据库配置**: `Backend/config/database.js`
- **初始化脚本**: `Backend/scripts/init-db.sql`

### 后台管理系统配置
- **环境变量文件**: `NewzoraAdmin/Backend/.env`
- **数据库配置**: `NewzoraAdmin/Backend/config/database.js`
- **初始化脚本**: `NewzoraAdmin/Backend/scripts/init-db.sql`

## 🚀 快速开始

### 1. 使用数据库管理工具（推荐）

```bash
# 完整设置两个数据库
node scripts/database-manager.js setup

# 单独创建数据库
node scripts/database-manager.js create web    # 主站前台
node scripts/database-manager.js create admin  # 后台管理

# 初始化数据库
node scripts/database-manager.js init web     # 主站前台
node scripts/database-manager.js init admin   # 后台管理

# 检查数据库状态
node scripts/database-manager.js status web
node scripts/database-manager.js status admin
```

### 2. 手动创建数据库

```sql
-- 创建主站前台数据库
CREATE DATABASE "PostgreSQL-newzora_web" WITH ENCODING='UTF8';

-- 创建后台管理系统数据库
CREATE DATABASE "PostgreSQL-newzora_admin" WITH ENCODING='UTF8';
```

### 3. 运行初始化脚本

```bash
# 主站前台
cd Backend
psql -U postgres -h localhost -d "PostgreSQL-newzora_web" -f scripts/init-db.sql

# 后台管理系统
cd NewzoraAdmin/Backend
psql -U postgres -h localhost -d "PostgreSQL-newzora_admin" -f scripts/init-db.sql
node scripts/initDatabase.js
```

## 🐳 Docker 部署

Docker Compose 配置已更新为支持两个独立的数据库服务：

```yaml
services:
  postgres-web:      # 主站前台数据库 (端口: 5432)
  postgres-admin:    # 后台管理数据库 (端口: 5433)
  backend:           # 主站后端服务
  admin-backend:     # 管理后台后端服务
  frontend:          # 主站前端服务
  admin-frontend:    # 管理后台前端服务
```

启动所有服务：
```bash
cd deployment/docker
docker-compose up -d
```

## 🔐 安全考虑

### 数据隔离
- 主站前台和后台管理系统使用完全独立的数据库
- 不同的数据库密码确保安全隔离
- 各自的用户权限和访问控制

### 密码安全
- 主站前台: `wasd080980!` - 复杂密码包含特殊字符
- 后台管理: `QWasd080980!` - 更复杂的密码组合
- 生产环境建议使用环境变量管理密码

### 网络安全
- Docker 环境中数据库服务仅在内部网络可访问
- 不同端口映射避免冲突
- 可配置防火墙规则限制外部访问

## 📊 数据库管理

### 备份策略
```bash
# 备份主站前台数据库
node scripts/database-manager.js backup web

# 备份后台管理数据库
node scripts/database-manager.js backup admin
```

### 恢复数据库
```bash
# 恢复主站前台数据库
node scripts/database-manager.js restore web /path/to/backup.sql

# 恢复后台管理数据库
node scripts/database-manager.js restore admin /path/to/backup.sql
```

### 监控和维护
- 定期检查数据库连接状态
- 监控数据库性能指标
- 定期清理日志和临时数据
- 更新数据库统计信息

## 🔄 迁移指南

### 从旧配置迁移

如果您之前使用的是单一数据库配置，请按以下步骤迁移：

1. **备份现有数据**
   ```bash
   pg_dump -U postgres -h localhost newzora > backup_old.sql
   ```

2. **创建新数据库**
   ```bash
   node scripts/database-manager.js setup
   ```

3. **迁移数据**（根据业务需求选择性迁移）
   - 用户数据 → 主站前台数据库
   - 管理员数据 → 后台管理数据库
   - 文章内容 → 主站前台数据库
   - 系统设置 → 后台管理数据库

4. **更新应用配置**
   - 确认环境变量正确设置
   - 测试数据库连接
   - 验证应用功能

## 🛠️ 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数（主机、端口、用户名、密码）
   - 检查防火墙设置

2. **权限错误**
   - 确认数据库用户权限
   - 检查数据库和表的访问权限
   - 验证角色配置

3. **端口冲突**
   - 主站前台数据库使用端口 5432
   - 后台管理数据库使用端口 5433（Docker）
   - 确保端口未被其他服务占用

### 调试命令

```bash
# 检查数据库状态
node scripts/database-manager.js status web
node scripts/database-manager.js status admin

# 测试连接
psql -U postgres -h localhost -p 5432 -d "PostgreSQL-newzora_web"
psql -U postgres -h localhost -p 5433 -d "PostgreSQL-newzora_admin"

# 查看运行中的容器
docker ps | grep postgres
```

## 📞 支持

如果在数据库配置过程中遇到问题，请：

1. 检查本文档的故障排除部分
2. 查看应用日志文件
3. 验证环境变量配置
4. 确认数据库服务状态

---

**注意**: 此配置确保了主站前台和后台管理系统的数据完全隔离，提高了系统的安全性和可维护性。