/**
 * 设备检测工具
 * 用于区分PC端和移动端，实现不同的翻译策略
 * 遵循项目规则：TypeScript强类型、完整错误处理、客户端安全检查
 */

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  userAgent: string;
  platform: string;
  screenWidth: number;
  screenHeight: number;
}

/**
 * 检测是否为移动设备
 */
export const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false;

  // 检查用户代理
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = [
    'android', 'webos', 'iphone', 'ipad', 'ipod', 
    'blackberry', 'windows phone', 'mobile', 'opera mini'
  ];

  const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));

  // 检查屏幕尺寸
  const isMobileScreen = window.innerWidth <= 768;

  // 检查触摸支持
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

  return isMobileUA || (isMobileScreen && isTouchDevice);
};

/**
 * 检测是否为平板设备
 */
export const isTabletDevice = (): boolean => {
  if (typeof window === 'undefined') return false;

  const userAgent = navigator.userAgent.toLowerCase();
  const isTabletUA = userAgent.includes('ipad') || 
    (userAgent.includes('android') && !userAgent.includes('mobile'));

  const isTabletScreen = window.innerWidth > 768 && window.innerWidth <= 1024;
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

  return isTabletUA || (isTabletScreen && isTouchDevice);
};

/**
 * 检测是否为桌面设备
 */
export const isDesktopDevice = (): boolean => {
  if (typeof window === 'undefined') return true; // 服务端默认为桌面

  return !isMobileDevice() && !isTabletDevice();
};

/**
 * 获取完整的设备信息
 */
export const getDeviceInfo = (): DeviceInfo => {
  if (typeof window === 'undefined') {
    return {
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      isTouchDevice: false,
      userAgent: '',
      platform: '',
      screenWidth: 1920,
      screenHeight: 1080
    };
  }

  const isMobile = isMobileDevice();
  const isTablet = isTabletDevice();
  const isDesktop = isDesktopDevice();

  return {
    isMobile,
    isTablet,
    isDesktop,
    isTouchDevice: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight
  };
};

/**
 * 检测操作系统
 */
export const getOperatingSystem = (): string => {
  if (typeof window === 'undefined') return 'unknown';

  const userAgent = navigator.userAgent;
  const platform = navigator.platform;

  if (/Mac|iPhone|iPad|iPod/.test(userAgent)) return 'ios';
  if (/Android/.test(userAgent)) return 'android';
  if (/Windows/.test(userAgent)) return 'windows';
  if (/Linux/.test(userAgent)) return 'linux';
  if (/Mac/.test(platform)) return 'macos';

  return 'unknown';
};

/**
 * 检测浏览器类型
 */
export const getBrowserType = (): string => {
  if (typeof window === 'undefined') return 'unknown';

  const userAgent = navigator.userAgent;

  if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) return 'chrome';
  if (userAgent.includes('Firefox')) return 'firefox';
  if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'safari';
  if (userAgent.includes('Edg')) return 'edge';
  if (userAgent.includes('Opera')) return 'opera';

  return 'unknown';
};

/**
 * 检测是否支持浏览器自动翻译
 */
export const supportsBrowserTranslation = (): boolean => {
  if (typeof window === 'undefined') return false;

  const browser = getBrowserType();
  const isDesktop = isDesktopDevice();

  // 桌面版Chrome、Edge、Firefox支持自动翻译
  return isDesktop && ['chrome', 'edge', 'firefox'].includes(browser);
};

/**
 * 检测是否应该使用系统翻译
 */
export const shouldUseSystemTranslation = (): boolean => {
  const deviceInfo = getDeviceInfo();
  const os = getOperatingSystem();

  // 移动设备且为iOS或Android系统
  return (deviceInfo.isMobile || deviceInfo.isTablet) && ['ios', 'android'].includes(os);
};

// React Hook将在单独的文件中定义，避免循环依赖

/**
 * 获取设备翻译策略
 */
export const getTranslationStrategy = (): 'browser' | 'system' | 'none' => {
  if (supportsBrowserTranslation()) {
    return 'browser';
  }

  if (shouldUseSystemTranslation()) {
    return 'system';
  }

  return 'none';
};
