'use client';

import { useState, useEffect } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  PhotoIcon,
  ChatBubbleLeftIcon,
  UserIcon,
} from '@heroicons/react/24/outline';

interface ContentReview {
  id: number;
  contentType: 'article' | 'draft' | 'comment' | 'media_file' | 'user_profile';
  contentId: number;
  status: 'pending' | 'approved' | 'rejected' | 'escalated';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  aiFlags: string[];
  submittedAt: string;
  reviewedAt?: string;
  submitter: {
    id: number;
    username: string;
    email: string;
  };
  reviewer?: {
    id: number;
    username: string;
  };
  reviewNotes?: string;
  content?: any;
}

export default function ContentReviewsPage() {
  const { user, session } = useSimpleAuth();
  const router = useRouter();
  const [reviews, setReviews] = useState<ContentReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');
  const [selectedReview, setSelectedReview] = useState<ContentReview | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    if (!user || !session) {
      router.push('/auth/login');
      return;
    }

    // Check if user has moderator or admin role
    if (!user.role || !['moderator', 'admin'].includes(user.role)) {
      router.push('/unauthorized');
      return;
    }

    fetchReviews();
  }, [user, session, router, filter]);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      setError(null);

      const endpoint =
        filter === 'pending'
          ? 'http://localhost:5000/api/reviews/pending'
          : `http://localhost:5000/api/reviews/my-reviews?status=${filter === 'all' ? '' : filter}`;

      const response = await fetch(endpoint, {
        headers: {
          Authorization: `Bearer ${session?.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setReviews(data.data.reviews || []);
      } else {
        setError('Failed to fetch reviews');
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      setError('Failed to fetch reviews');
    } finally {
      setLoading(false);
    }
  };

  const handleReviewAction = async (
    reviewId: number,
    action: 'approve' | 'reject' | 'escalate'
  ) => {
    try {
      setProcessing(true);

      const response = await fetch(`http://localhost:5000/api/reviews/${reviewId}/${action}`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${session?.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: reviewNotes,
        }),
      });

      if (response.ok) {
        // Refresh reviews
        fetchReviews();
        setSelectedReview(null);
        setReviewNotes('');
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Action failed');
      }
    } catch (error) {
      console.error('Error processing review:', error);
      setError('Action failed');
    } finally {
      setProcessing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'escalated':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getContentTypeIcon = (contentType: string) => {
    switch (contentType) {
      case 'article':
      case 'draft':
        return <DocumentTextIcon className="w-5 h-5" />;
      case 'media_file':
        return <PhotoIcon className="w-5 h-5" />;
      case 'comment':
        return <ChatBubbleLeftIcon className="w-5 h-5" />;
      case 'user_profile':
        return <UserIcon className="w-5 h-5" />;
      default:
        return <DocumentTextIcon className="w-5 h-5" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!user || !['moderator', 'admin'].includes(user.role || '')) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link
              href="/content"
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="w-6 h-6" />
            </Link>

            <div>
              <h1 className="text-2xl font-bold text-gray-900">Content Reviews</h1>
              <p className="text-gray-600">Review and moderate content submissions</p>
            </div>
          </div>

          {/* Filter Buttons */}
          <div className="flex space-x-2">
            {['pending', 'all', 'approved', 'rejected'].map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setFilter(filterOption as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  filter === filterOption
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <p className="text-red-600">{error}</p>
            <button
              onClick={fetchReviews}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Reviews List */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow">
                <div className="p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">
                    Reviews ({reviews.length})
                  </h2>

                  {reviews.length === 0 ? (
                    <div className="text-center py-12">
                      <EyeIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No reviews found</h3>
                      <p className="text-gray-500">
                        {filter === 'pending'
                          ? 'No pending reviews at the moment'
                          : `No ${filter} reviews found`}
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {reviews.map((review) => (
                        <div
                          key={review.id}
                          className={`border rounded-lg p-4 cursor-pointer transition-all ${
                            selectedReview?.id === review.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                          }`}
                          onClick={() => setSelectedReview(review)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0 mt-1">
                                {getContentTypeIcon(review.contentType)}
                              </div>

                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-2">
                                  <span className="font-medium text-gray-900 capitalize">
                                    {review.contentType.replace('_', ' ')} #{review.contentId}
                                  </span>

                                  <span
                                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(review.status)}`}
                                  >
                                    {review.status}
                                  </span>
                                </div>

                                <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                                  <span>By {review.submitter.username}</span>
                                  <span>•</span>
                                  <span>{formatDate(review.submittedAt)}</span>
                                </div>

                                <div className="flex items-center space-x-2">
                                  <span
                                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(review.priority)}`}
                                  >
                                    {review.priority} priority
                                  </span>

                                  <span
                                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(review.riskLevel)}`}
                                  >
                                    {review.riskLevel} risk
                                  </span>
                                </div>

                                {review.aiFlags.length > 0 && (
                                  <div className="mt-2">
                                    <div className="flex flex-wrap gap-1">
                                      {review.aiFlags.map((flag, index) => (
                                        <span
                                          key={index}
                                          className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800"
                                        >
                                          <ExclamationTriangleIcon className="w-3 h-3 mr-1" />
                                          {flag}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Review Details Panel */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow sticky top-8">
                {selectedReview ? (
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Review Details</h3>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Content Type
                        </label>
                        <p className="text-sm text-gray-900 capitalize">
                          {selectedReview.contentType.replace('_', ' ')}
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Submitter
                        </label>
                        <p className="text-sm text-gray-900">
                          {selectedReview.submitter.username} ({selectedReview.submitter.email})
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Submitted At
                        </label>
                        <p className="text-sm text-gray-900">
                          {formatDate(selectedReview.submittedAt)}
                        </p>
                      </div>

                      {selectedReview.aiFlags.length > 0 && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            AI Flags
                          </label>
                          <div className="space-y-1">
                            {selectedReview.aiFlags.map((flag, index) => (
                              <span
                                key={index}
                                className="inline-block px-2 py-1 rounded-full text-xs bg-red-100 text-red-800 mr-1 mb-1"
                              >
                                {flag}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {selectedReview.status === 'pending' && (
                        <>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Review Notes
                            </label>
                            <textarea
                              value={reviewNotes}
                              onChange={(e) => setReviewNotes(e.target.value)}
                              placeholder="Add your review notes..."
                              rows={4}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>

                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleReviewAction(selectedReview.id, 'approve')}
                              disabled={processing}
                              className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center justify-center"
                            >
                              <CheckCircleIcon className="w-4 h-4 mr-2" />
                              Approve
                            </button>

                            <button
                              onClick={() => handleReviewAction(selectedReview.id, 'reject')}
                              disabled={processing}
                              className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center justify-center"
                            >
                              <XCircleIcon className="w-4 h-4 mr-2" />
                              Reject
                            </button>
                          </div>

                          <button
                            onClick={() => handleReviewAction(selectedReview.id, 'escalate')}
                            disabled={processing}
                            className="w-full bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors disabled:opacity-50 flex items-center justify-center"
                          >
                            <ExclamationTriangleIcon className="w-4 h-4 mr-2" />
                            Escalate
                          </button>
                        </>
                      )}

                      {selectedReview.reviewNotes && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Review Notes
                          </label>
                          <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">
                            {selectedReview.reviewNotes}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="p-6 text-center">
                    <EyeIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">Select a review to see details</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
