/**
 * Real-time Data Service for Newzora
 * 实时数据同步服务，替代所有模拟数据
 */

import { supabase } from '@/lib/supabase';

export interface RealTimeStats {
  views: number;
  likes: number;
  comments: number;
  shares: number;
  bookmarks: number;
  followers: number;
  following: number;
}

export interface RealTimeComment {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar: string;
  };
  createdAt: string;
  likes: number;
  replies: RealTimeComment[];
}

export interface RealTimeRevenue {
  totalRevenue: number;
  monthlyRevenue: number;
  dailyRevenue: number;
  balance: number;
  earnings: number;
}

export interface RealTimeAnalytics {
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  totalShares: number;
  subscriberCount: number;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  shareCount: number;
}

class RealTimeDataService {
  private subscribers: Map<string, Set<(data: any) => void>> = new Map();

  /**
   * 获取实时统计数据
   */
  async getStats(contentId: string, contentType: 'work' | 'article' | 'video'): Promise<RealTimeStats> {
    try {
      const { data, error } = await supabase
        .from('content_stats')
        .select('*')
        .eq('content_id', contentId)
        .eq('content_type', contentType)
        .single();

      if (error) {
        console.error('Error fetching stats:', error);
        return this.getEmptyStats();
      }

      return data || this.getEmptyStats();
    } catch (error) {
      console.error('Error in getStats:', error);
      return this.getEmptyStats();
    }
  }

  /**
   * 获取实时评论数据
   */
  async getComments(contentId: string, contentType: 'work' | 'article' | 'video'): Promise<RealTimeComment[]> {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          id,
          content,
          created_at,
          likes,
          author:profiles(id, name, avatar),
          replies:comments(
            id,
            content,
            created_at,
            likes,
            author:profiles(id, name, avatar)
          )
        `)
        .eq('content_id', contentId)
        .eq('content_type', contentType)
        .eq('parent_id', null)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching comments:', error);
        return [];
      }

      return this.formatComments(data || []);
    } catch (error) {
      console.error('Error in getComments:', error);
      return [];
    }
  }

  /**
   * 获取实时收益数据
   */
  async getRevenue(userId: string): Promise<RealTimeRevenue> {
    try {
      const { data, error } = await supabase
        .from('user_revenue')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Error fetching revenue:', error);
        return this.getEmptyRevenue();
      }

      return data || this.getEmptyRevenue();
    } catch (error) {
      console.error('Error in getRevenue:', error);
      return this.getEmptyRevenue();
    }
  }

  /**
   * 获取实时分析数据
   */
  async getAnalytics(userId: string): Promise<RealTimeAnalytics> {
    try {
      const { data, error } = await supabase
        .from('user_analytics')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Error fetching analytics:', error);
        return this.getEmptyAnalytics();
      }

      return data || this.getEmptyAnalytics();
    } catch (error) {
      console.error('Error in getAnalytics:', error);
      return this.getEmptyAnalytics();
    }
  }

  /**
   * 订阅实时数据更新
   */
  subscribeToStats(contentId: string, contentType: string, callback: (stats: RealTimeStats) => void) {
    const key = `stats_${contentType}_${contentId}`;
    
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    
    this.subscribers.get(key)!.add(callback);

    // 设置 Supabase 实时订阅
    const subscription = supabase
      .channel(`stats_${contentId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'content_stats',
          filter: `content_id=eq.${contentId}`
        },
        (payload) => {
          this.notifySubscribers(key, payload.new);
        }
      )
      .subscribe();

    // 返回取消订阅函数
    return () => {
      this.subscribers.get(key)?.delete(callback);
      if (this.subscribers.get(key)?.size === 0) {
        this.subscribers.delete(key);
        subscription.unsubscribe();
      }
    };
  }

  /**
   * 订阅实时评论更新
   */
  subscribeToComments(contentId: string, contentType: string, callback: (comments: RealTimeComment[]) => void) {
    const key = `comments_${contentType}_${contentId}`;
    
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    
    this.subscribers.get(key)!.add(callback);

    const subscription = supabase
      .channel(`comments_${contentId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'comments',
          filter: `content_id=eq.${contentId}`
        },
        async () => {
          // 重新获取评论数据
          const comments = await this.getComments(contentId, contentType as any);
          this.notifySubscribers(key, comments);
        }
      )
      .subscribe();

    return () => {
      this.subscribers.get(key)?.delete(callback);
      if (this.subscribers.get(key)?.size === 0) {
        this.subscribers.delete(key);
        subscription.unsubscribe();
      }
    };
  }

  /**
   * 更新统计数据
   */
  async updateStats(contentId: string, contentType: string, field: keyof RealTimeStats, increment: number = 1) {
    try {
      const { error } = await supabase.rpc('increment_stat', {
        content_id: contentId,
        content_type: contentType,
        field_name: field,
        increment_value: increment
      });

      if (error) {
        console.error('Error updating stats:', error);
      }
    } catch (error) {
      console.error('Error in updateStats:', error);
    }
  }

  /**
   * 添加评论
   */
  async addComment(contentId: string, contentType: string, content: string, parentId?: string) {
    try {
      const { data, error } = await supabase
        .from('comments')
        .insert({
          content_id: contentId,
          content_type: contentType,
          content: content,
          parent_id: parentId || null,
          author_id: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) {
        console.error('Error adding comment:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in addComment:', error);
      return null;
    }
  }

  // 私有辅助方法
  private getEmptyStats(): RealTimeStats {
    return {
      views: 0,
      likes: 0,
      comments: 0,
      shares: 0,
      bookmarks: 0,
      followers: 0,
      following: 0
    };
  }

  private getEmptyRevenue(): RealTimeRevenue {
    return {
      totalRevenue: 0,
      monthlyRevenue: 0,
      dailyRevenue: 0,
      balance: 0,
      earnings: 0
    };
  }

  private getEmptyAnalytics(): RealTimeAnalytics {
    return {
      totalViews: 0,
      totalLikes: 0,
      totalComments: 0,
      totalShares: 0,
      subscriberCount: 0,
      viewCount: 0,
      likeCount: 0,
      commentCount: 0,
      shareCount: 0
    };
  }

  private formatComments(rawComments: any[]): RealTimeComment[] {
    return rawComments.map(comment => ({
      id: comment.id,
      content: comment.content,
      author: {
        id: comment.author?.id || '',
        name: comment.author?.name || 'Anonymous',
        avatar: comment.author?.avatar || ''
      },
      createdAt: comment.created_at,
      likes: comment.likes || 0,
      replies: comment.replies ? this.formatComments(comment.replies) : []
    }));
  }

  private notifySubscribers(key: string, data: any) {
    const callbacks = this.subscribers.get(key);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }
}

export const realTimeDataService = new RealTimeDataService();
