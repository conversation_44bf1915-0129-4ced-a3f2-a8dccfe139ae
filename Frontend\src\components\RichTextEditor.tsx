'use client';

import React, { useRef, useEffect } from 'react';

interface RichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

const RichTextEditor = ({
  value,
  onChange,
  placeholder = 'Write your content here...',
  className = ''
}: RichTextEditorProps) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);
  const audioInputRef = useRef<HTMLInputElement>(null);
  const isUpdatingRef = useRef(false);

  // 同步外部value变化，避免闪烁
  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      isUpdatingRef.current = true;
      editorRef.current.innerHTML = value;
      isUpdatingRef.current = false;
    }
  }, [value]);

  const handleChange = () => {
    if (editorRef.current && !isUpdatingRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  const executeCommand = (command: string, value: string = '') => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    handleChange();
  };

  const insertMedia = (type: 'image' | 'video' | 'audio') => {
    const inputRef = type === 'image' ? imageInputRef : type === 'video' ? videoInputRef : audioInputRef;
    inputRef.current?.click();
  };

  const handleMediaUpload = (e: React.ChangeEvent<HTMLInputElement>, type: 'image' | 'video' | 'audio') => {
    const file = e.target.files?.[0];
    if (file && editorRef.current) {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result && editorRef.current) {
          const src = event.target.result as string;
          let element = '';
          
          if (type === 'image') {
            element = `<img src="${src}" alt="Uploaded image" style="max-width: 100%; height: auto;" />`;
          } else if (type === 'video') {
            element = `<video controls style="max-width: 100%; height: auto;"><source src="${src}" type="${file.type}" /></video>`;
          } else {
            element = `<audio controls><source src="${src}" type="${file.type}" /></audio>`;
          }
          
          document.execCommand('insertHTML', false, element);
          handleChange();
        }
      };
      reader.readAsDataURL(file);
    }
    e.target.value = '';
  };

  const insertLink = () => {
    const url = prompt('Enter URL:');
    if (url) executeCommand('createLink', url);
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Clean Toolbar */}
      <div className="border-b border-gray-100 bg-gray-50/50 px-4 py-3">
        <div className="flex flex-wrap items-center gap-2">
          {/* Text Formatting */}
          <div className="flex items-center border-r border-gray-200 pr-3 mr-3">
            <button
              type="button"
              onClick={() => executeCommand('bold')}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
              title="Bold"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M15.6 10.79c.97-.67 1.65-1.77 1.65-2.79 0-2.26-1.75-4-4-4H7v14h7.04c2.09 0 3.71-1.7 3.71-3.79 0-1.52-.86-2.82-2.15-3.42zM10 6.5h3c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-3v-3zm3.5 9H10v-3h3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5z"/>
              </svg>
            </button>
            <button
              type="button"
              onClick={() => executeCommand('italic')}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
              title="Italic"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4h-8z"/>
              </svg>
            </button>
            <button
              type="button"
              onClick={() => executeCommand('underline')}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
              title="Underline"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6zm-7 2v2h14v-2H5z"/>
              </svg>
            </button>
          </div>

          {/* Headings */}
          <div className="flex items-center border-r border-gray-200 pr-3 mr-3">
            <button
              type="button"
              onClick={() => executeCommand('formatBlock', 'h1')}
              className="px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
              title="Heading 1"
            >
              H1
            </button>
            <button
              type="button"
              onClick={() => executeCommand('formatBlock', 'h2')}
              className="px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
              title="Heading 2"
            >
              H2
            </button>
            <button
              type="button"
              onClick={() => executeCommand('formatBlock', 'h3')}
              className="px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
              title="Heading 3"
            >
              H3
            </button>
          </div>

          {/* Alignment */}
          <div className="flex items-center border-r border-gray-200 pr-3 mr-3">
            <button
              type="button"
              onClick={() => executeCommand('justifyLeft')}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
              title="Align Left"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M15 15H3v2h12v-2zm0-8H3v2h12V7zM3 13h18v-2H3v2zm0 8h18v-2H3v2zM3 3v2h18V3H3z"/>
              </svg>
            </button>
            <button
              type="button"
              onClick={() => executeCommand('justifyCenter')}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
              title="Align Center"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M7 15v2h10v-2H7zm-4 6h18v-2H3v2zm0-8h18v-2H3v2zm4-6v2h10V7H7zM3 3v2h18V3H3z"/>
              </svg>
            </button>
            <button
              type="button"
              onClick={() => executeCommand('justifyRight')}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
              title="Align Right"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 21h18v-2H3v2zm6-4h12v-2H9v2zm-6-4h18v-2H3v2zm6-4h12V7H9v2zM3 3v2h18V3H3z"/>
              </svg>
            </button>
          </div>

          {/* Media */}
          <div className="flex items-center">
            <button
              type="button"
              onClick={() => insertMedia('image')}
              className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
              title="Insert Image"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
              </svg>
            </button>
            <button
              type="button"
              onClick={() => insertMedia('video')}
              className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
              title="Insert Video"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
              </svg>
            </button>
            <button
              type="button"
              onClick={() => insertMedia('audio')}
              className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-md transition-colors"
              title="Insert Audio"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
              </svg>
            </button>
            <button
              type="button"
              onClick={insertLink}
              className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors"
              title="Insert Link"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H6.9C3.58 7 1 9.58 1 12.9s2.58 5.9 5.9 5.9h4v-1.9H6.9c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9.1-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c3.31 0 5.9-2.58 5.9-5.9S20.41 7 17.1 7z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Editor Area */}
      <div className="relative">
        <div
          ref={editorRef}
          contentEditable
          onInput={handleChange}
          className="p-6 min-h-96 focus:outline-none text-gray-900 leading-relaxed"
          style={{
            wordBreak: 'break-word',
            fontSize: '16px',
            lineHeight: '1.6'
          }}
          suppressContentEditableWarning={true}
        />
        {!value && (
          <div className="absolute top-6 left-6 text-gray-400 pointer-events-none select-none">
            <span className="text-base">{placeholder}</span>
          </div>
        )}
      </div>

      {/* Hidden file inputs */}
      <input
        ref={imageInputRef}
        type="file"
        accept="image/*"
        onChange={(e) => handleMediaUpload(e, 'image')}
        className="hidden"
      />
      <input
        ref={videoInputRef}
        type="file"
        accept="video/*"
        onChange={(e) => handleMediaUpload(e, 'video')}
        className="hidden"
      />
      <input
        ref={audioInputRef}
        type="file"
        accept="audio/*"
        onChange={(e) => handleMediaUpload(e, 'audio')}
        className="hidden"
      />
    </div>
  );
};

export default RichTextEditor;