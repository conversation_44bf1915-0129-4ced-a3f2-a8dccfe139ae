/**
 * React DOM Error Fix Utilities
 * 专门用于修复 insertBefore, removeChild 等DOM操作错误
 */

// 全局DOM错误修复标志
let isDOMFixInitialized = false;

/**
 * 初始化React DOM错误修复
 */
export function initializeReactDOMFix() {
  if (isDOMFixInitialized || typeof window === 'undefined') {
    return;
  }

  // 修复原生DOM方法
  patchDOMMethods();
  
  // 修复React相关的DOM操作
  patchReactDOM();
  
  isDOMFixInitialized = true;
  console.log('React DOM fix initialized');
}

/**
 * 修复原生DOM方法
 */
function patchDOMMethods() {
  // 修复 insertBefore 方法
  const originalInsertBefore = Node.prototype.insertBefore;
  Node.prototype.insertBefore = function(newNode: Node, referenceNode: Node | null) {
    try {
      // 检查节点是否有效
      if (!newNode || newNode === this) {
        console.warn('Invalid insertBefore operation: invalid newNode');
        return newNode;
      }

      // 检查引用节点是否是当前节点的子节点
      if (referenceNode && !this.contains(referenceNode)) {
        console.warn('Invalid insertBefore operation: referenceNode is not a child');
        // 降级为 appendChild
        return this.appendChild(newNode);
      }

      // 检查新节点是否已经是子节点
      if (this.contains(newNode)) {
        console.warn('insertBefore: newNode is already a child');
        return newNode;
      }

      return originalInsertBefore.call(this, newNode, referenceNode);
    } catch (error) {
      console.warn('insertBefore error caught and handled:', error);
      // 尝试降级为 appendChild
      try {
        return this.appendChild(newNode);
      } catch (fallbackError) {
        console.warn('appendChild fallback also failed:', fallbackError);
        return newNode;
      }
    }
  };

  // 修复 removeChild 方法
  const originalRemoveChild = Node.prototype.removeChild;
  Node.prototype.removeChild = function(child: Node) {
    try {
      if (!child || !this.contains(child)) {
        console.warn('Invalid removeChild operation: child is not a child of this node');
        return child;
      }
      return originalRemoveChild.call(this, child);
    } catch (error) {
      console.warn('removeChild error caught and handled:', error);
      return child;
    }
  };

  // 修复 appendChild 方法
  const originalAppendChild = Node.prototype.appendChild;
  Node.prototype.appendChild = function(child: Node) {
    try {
      if (!child || child === this) {
        console.warn('Invalid appendChild operation: invalid child');
        return child;
      }

      if (this.contains(child)) {
        console.warn('appendChild: child is already a child');
        return child;
      }

      return originalAppendChild.call(this, child);
    } catch (error) {
      console.warn('appendChild error caught and handled:', error);
      return child;
    }
  };
}

/**
 * 修复React DOM相关操作
 */
function patchReactDOM() {
  // 监听React的DOM操作错误
  const originalCreateElement = document.createElement;
  document.createElement = function(tagName: string, options?: ElementCreationOptions) {
    try {
      const element = originalCreateElement.call(this, tagName, options);
      
      // 为元素添加安全标记
      (element as any).__reactDOMSafe = true;
      
      return element;
    } catch (error) {
      console.warn('createElement error:', error);
      throw error;
    }
  };

  // 修复React的事件处理
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(type: string, listener: EventListenerOrEventListenerObject | null, options?: boolean | AddEventListenerOptions) {
    try {
      return originalAddEventListener.call(this, type, listener, options);
    } catch (error) {
      console.warn('addEventListener error caught:', error);
    }
  };

  const originalRemoveEventListener = EventTarget.prototype.removeEventListener;
  EventTarget.prototype.removeEventListener = function(type: string, listener: EventListenerOrEventListenerObject | null, options?: boolean | EventListenerOptions) {
    try {
      return originalRemoveEventListener.call(this, type, listener, options);
    } catch (error) {
      console.warn('removeEventListener error caught:', error);
    }
  };
}

/**
 * 安全的DOM操作包装器
 */
export const safeDOMOperations = {
  insertBefore: (parent: Node, newNode: Node, referenceNode: Node | null): boolean => {
    try {
      if (!parent || !newNode) return false;
      
      if (referenceNode && !parent.contains(referenceNode)) {
        parent.appendChild(newNode);
      } else {
        parent.insertBefore(newNode, referenceNode);
      }
      return true;
    } catch (error) {
      console.warn('Safe insertBefore failed:', error);
      return false;
    }
  },

  removeChild: (parent: Node, child: Node): boolean => {
    try {
      if (!parent || !child || !parent.contains(child)) return false;
      parent.removeChild(child);
      return true;
    } catch (error) {
      console.warn('Safe removeChild failed:', error);
      return false;
    }
  },

  appendChild: (parent: Node, child: Node): boolean => {
    try {
      if (!parent || !child || parent.contains(child)) return false;
      parent.appendChild(child);
      return true;
    } catch (error) {
      console.warn('Safe appendChild failed:', error);
      return false;
    }
  }
};

/**
 * React组件安全渲染检查
 */
export function validateReactElement(element: React.ReactElement): boolean {
  try {
    // 检查元素是否有效
    if (!element || typeof element !== 'object') {
      return false;
    }

    // 检查是否有key属性（对于列表渲染）
    if (Array.isArray(element) && element.some(item => !item.key)) {
      console.warn('React elements in array missing key props');
    }

    return true;
  } catch (error) {
    console.warn('React element validation failed:', error);
    return false;
  }
}

/**
 * 清理DOM错误修复
 */
export function cleanupReactDOMFix() {
  isDOMFixInitialized = false;
  console.log('React DOM fix cleaned up');
}
