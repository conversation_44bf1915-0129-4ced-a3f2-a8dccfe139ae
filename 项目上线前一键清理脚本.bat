@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Newzora 项目上线前一键清理脚本 (Windows批处理版本)
:: 版本: 1.0.0
:: 作者: Newzora Team
:: 日期: 2025-01-04

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Newzora 上线前清理脚本                    ║
echo ║                        Version 1.0.0                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [INFO] 开始执行 Newzora 项目上线前清理...
echo.

:: 1. 清理开发和测试文件
echo 🧪 清理开发和测试文件...
if exist "Frontend\__tests__" (
    echo [CLEAN] 删除前端测试目录...
    rmdir /s /q "Frontend\__tests__" 2>nul
)

if exist "Frontend\src\app\test-fixes" (
    echo [CLEAN] 删除测试修复页面...
    rmdir /s /q "Frontend\src\app\test-fixes" 2>nul
)

if exist "Frontend\src\components\__tests__" (
    echo [CLEAN] 删除组件测试文件...
    rmdir /s /q "Frontend\src\components\__tests__" 2>nul
)

if exist "NewzoraAdmin\Frontend\__tests__" (
    echo [CLEAN] 删除后台测试目录...
    rmdir /s /q "NewzoraAdmin\Frontend\__tests__" 2>nul
)

if exist "Backend\__tests__" (
    echo [CLEAN] 删除后端测试目录...
    rmdir /s /q "Backend\__tests__" 2>nul
)

if exist "Backend\tests" (
    echo [CLEAN] 删除后端测试文件...
    rmdir /s /q "Backend\tests" 2>nul
)

if exist "tests" (
    echo [CLEAN] 删除根目录测试文件...
    rmdir /s /q "tests" 2>nul
)

:: 2. 清理临时和缓存文件
echo.
echo 🗂️ 清理临时和缓存文件...

if exist "Frontend\.next" (
    echo [CLEAN] 删除 Next.js 构建缓存...
    rmdir /s /q "Frontend\.next" 2>nul
)

if exist "Frontend\node_modules\.cache" (
    echo [CLEAN] 删除 Node.js 缓存...
    rmdir /s /q "Frontend\node_modules\.cache" 2>nul
)

if exist "Frontend\tsconfig.tsbuildinfo" (
    echo [CLEAN] 删除 TypeScript 构建信息...
    del /q "Frontend\tsconfig.tsbuildinfo" 2>nul
)

if exist "NewzoraAdmin\Frontend\.next" (
    echo [CLEAN] 删除后台 Next.js 缓存...
    rmdir /s /q "NewzoraAdmin\Frontend\.next" 2>nul
)

if exist "NewzoraAdmin\Frontend\node_modules\.cache" (
    echo [CLEAN] 删除后台 Node.js 缓存...
    rmdir /s /q "NewzoraAdmin\Frontend\node_modules\.cache" 2>nul
)

if exist "NewzoraAdmin\Frontend\tsconfig.tsbuildinfo" (
    echo [CLEAN] 删除后台 TypeScript 构建信息...
    del /q "NewzoraAdmin\Frontend\tsconfig.tsbuildinfo" 2>nul
)

if exist "Backend\node_modules\.cache" (
    echo [CLEAN] 删除后端 Node.js 缓存...
    rmdir /s /q "Backend\node_modules\.cache" 2>nul
)

:: 清理日志文件
for %%f in (*.log npm-debug.log* yarn-debug.log* yarn-error.log*) do (
    if exist "%%f" (
        echo [CLEAN] 删除日志文件: %%f
        del /q "%%f" 2>nul
    )
)

if exist "Backend\logs" (
    echo [CLEAN] 清理后端日志文件...
    del /q "Backend\logs\*.log" 2>nul
)

:: 3. 清理开发工具文件
echo.
echo 🔧 清理开发工具文件...

if exist "Frontend\jest.config.js" (
    echo [CLEAN] 删除 Jest 配置文件...
    del /q "Frontend\jest.config.js" 2>nul
)

if exist "Frontend\jest.setup.js" (
    echo [CLEAN] 删除 Jest 设置文件...
    del /q "Frontend\jest.setup.js" 2>nul
)

if exist "NewzoraAdmin\Frontend\jest.config.js" (
    echo [CLEAN] 删除后台 Jest 配置...
    del /q "NewzoraAdmin\Frontend\jest.config.js" 2>nul
)

if exist "NewzoraAdmin\Frontend\jest.setup.js" (
    echo [CLEAN] 删除后台 Jest 设置...
    del /q "NewzoraAdmin\Frontend\jest.setup.js" 2>nul
)

if exist ".vscode" (
    echo [CLEAN] 删除 VS Code 配置目录...
    rmdir /s /q ".vscode" 2>nul
)

if exist ".idea" (
    echo [CLEAN] 删除 IntelliJ IDEA 配置...
    rmdir /s /q ".idea" 2>nul
)

:: 清理系统文件
for /r . %%f in (.DS_Store Thumbs.db *.swp *.swo) do (
    if exist "%%f" (
        echo [CLEAN] 删除系统文件: %%f
        del /q "%%f" 2>nul
    )
)

:: 4. 清理重复和备份文件
echo.
echo 📄 清理重复和备份文件...

for /r . %%f in (*.bak *.backup *.old *.orig *.tmp *.temp *~ *.new *.copy *-copy.* *-backup.*) do (
    if exist "%%f" (
        echo [CLEAN] 删除备份文件: %%f
        del /q "%%f" 2>nul
    )
)

:: 5. 扫描调试代码
echo.
echo 🐛 扫描调试代码...
echo [INFO] 正在扫描 console.log, debugger 等调试代码...

set "debug_found=0"

:: 扫描前端文件
for /r "Frontend\src" %%f in (*.js *.jsx *.ts *.tsx) do (
    findstr /i /c:"console.log" /c:"console.debug" /c:"debugger;" /c:"TODO:" /c:"FIXME:" "%%f" >nul 2>&1
    if !errorlevel! equ 0 (
        echo [WARN] 发现调试代码: %%f
        set /a debug_found+=1
    )
)

:: 扫描后台文件
if exist "NewzoraAdmin\Frontend\src" (
    for /r "NewzoraAdmin\Frontend\src" %%f in (*.js *.jsx *.ts *.tsx) do (
        findstr /i /c:"console.log" /c:"console.debug" /c:"debugger;" /c:"TODO:" /c:"FIXME:" "%%f" >nul 2>&1
        if !errorlevel! equ 0 (
            echo [WARN] 发现调试代码: %%f
            set /a debug_found+=1
        )
    )
)

:: 扫描后端文件
if exist "Backend" (
    for /r "Backend" %%f in (*.js *.ts) do (
        findstr /i /c:"console.log" /c:"console.debug" /c:"debugger;" /c:"TODO:" /c:"FIXME:" "%%f" >nul 2>&1
        if !errorlevel! equ 0 (
            echo [WARN] 发现调试代码: %%f
            set /a debug_found+=1
        )
    )
)

if !debug_found! equ 0 (
    echo [SUCCESS] 未发现调试代码，代码质量良好!
) else (
    echo [WARN] 发现 !debug_found! 个文件包含调试代码，建议手动清理
)

:: 6. 检查 package.json
echo.
echo 📦 检查 package.json 优化...

if exist "Frontend\package.json" (
    findstr /i /c:"jest" /c:"@testing-library" "Frontend\package.json" >nul 2>&1
    if !errorlevel! equ 0 (
        echo [WARN] Frontend/package.json 中发现测试依赖，建议手动移除
    )
)

if exist "NewzoraAdmin\Frontend\package.json" (
    findstr /i /c:"jest" /c:"@testing-library" "NewzoraAdmin\Frontend\package.json" >nul 2>&1
    if !errorlevel! equ 0 (
        echo [WARN] NewzoraAdmin/Frontend/package.json 中发现测试依赖，建议手动移除
    )
)

:: 7. 生成清理报告
echo.
echo 📊 生成清理报告...

echo # Newzora 项目清理报告 > cleanup-report.md
echo 生成时间: %date% %time% >> cleanup-report.md
echo. >> cleanup-report.md
echo ## 清理项目 >> cleanup-report.md
echo ✅ 开发和测试文件 >> cleanup-report.md
echo ✅ 临时和缓存文件 >> cleanup-report.md
echo ✅ 开发工具配置 >> cleanup-report.md
echo ✅ 重复和备份文件 >> cleanup-report.md
echo ✅ 调试代码扫描 >> cleanup-report.md
echo ✅ package.json 优化检查 >> cleanup-report.md
echo. >> cleanup-report.md
echo ## 建议的后续操作 >> cleanup-report.md
echo 1. 运行 npm run build 确保项目可以正常构建 >> cleanup-report.md
echo 2. 检查所有环境变量配置 >> cleanup-report.md
echo 3. 更新生产环境配置 >> cleanup-report.md
echo 4. 进行最终的功能测试 >> cleanup-report.md
echo 5. 准备部署脚本 >> cleanup-report.md

echo [SUCCESS] 清理报告已保存到: cleanup-report.md

:: 最终总结
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        清理完成!                            ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ 项目已准备好上线部署                                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [SUCCESS] Newzora 项目清理完成! 项目已准备好上线部署。
echo.
echo 🚀 下一步: 运行构建命令验证项目完整性
echo    Frontend: cd Frontend ^&^& npm run build
echo    Admin: cd NewzoraAdmin/Frontend ^&^& npm run build  
echo    Backend: cd Backend ^&^& npm start
echo.

pause
