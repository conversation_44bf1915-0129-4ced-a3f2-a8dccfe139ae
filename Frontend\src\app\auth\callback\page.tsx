'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { supabase } from '@/lib/supabase';

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const authContext = useSimpleAuth();
  const isAuthenticated = (authContext as any)?.isAuthenticated || false;
  const clearError = (authContext as any)?.clearError || (() => {});
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing email verification...');

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        clearError();

        // 获取URL中的认证参数
        const code = searchParams.get('code');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        if (error) {
          setStatus('error');
          setMessage(`Verification failed: ${errorDescription || error}`);
          return;
        }

        if (code) {
          // 使用授权码交换会话
          const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

          if (exchangeError) {
            setStatus('error');
            setMessage(`Verification failed: ${exchangeError.message}`);
            return;
          }

          if (data.session) {
            setStatus('success');
            setMessage('Email verified successfully! Redirecting...');

            // 延迟跳转，让用户看到成功消息
            setTimeout(() => {
              router.replace('/');
            }, 2000);
            return;
          }
        }

        // If no code parameter, check if already logged in
        if (isAuthenticated) {
          setStatus('success');
          setMessage('You are already logged in, redirecting...');
          setTimeout(() => {
            router.replace('/');
          }, 1000);
          return;
        }

        // Check authentication info in URL hash (legacy compatibility)
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = hashParams.get('access_token');
        const refreshToken = hashParams.get('refresh_token');

        if (accessToken && refreshToken) {
          const { data, error: sessionError } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          });

          if (sessionError) {
            setStatus('error');
            setMessage(`会话设置失败: ${sessionError.message}`);
            return;
          }

          setStatus('success');
          setMessage('邮箱验证成功！正在跳转...');
          setTimeout(() => {
            router.replace('/');
          }, 2000);
          return;
        }

        // 如果没有找到任何认证信息，可能是OAuth回调
        setStatus('success');
        setMessage('登录成功！正在跳转...');
        setTimeout(() => {
          router.replace('/');
        }, 1000);

      } catch (err: any) {
        console.error('Auth callback error:', err);
        setStatus('error');
        setMessage(`处理验证时发生错误: ${err.message}`);
      }
    };

    handleAuthCallback();
  }, [searchParams, router, isAuthenticated, clearError]);

  const handleRetry = () => {
    router.replace('/auth/login');
  };

  const handleGoHome = () => {
    router.replace('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* 状态图标 */}
        <div className="mb-6">
          {status === 'loading' && (
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"></div>
          )}
          {status === 'success' && (
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          )}
          {status === 'error' && (
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          )}
        </div>

        {/* 状态标题 */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          {status === 'loading' && '验证中'}
          {status === 'success' && '验证成功！'}
          {status === 'error' && '验证失败'}
        </h1>

        {/* 状态消息 */}
        <p className={`text-sm mb-6 ${
          status === 'success' ? 'text-green-600' :
          status === 'error' ? 'text-red-600' :
          'text-gray-600'
        }`}>
          {message}
        </p>

        {/* 操作按钮 */}
        {status === 'error' && (
          <div className="space-y-3">
            <button
              onClick={handleRetry}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
              返回登录页面
            </button>
            <button
              onClick={handleGoHome}
              className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
            >
              返回首页
            </button>
          </div>
        )}

        {status === 'success' && (
          <button
            onClick={handleGoHome}
            className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
          >
            立即进入应用
          </button>
        )}
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
}