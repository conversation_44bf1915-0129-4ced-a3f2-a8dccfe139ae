'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Header from '@/components/Header';
import CategorySidebar from '@/components/CategorySidebar';
import WorkCard from '@/components/WorkCard';
import AdBanner from '@/components/AdBanner';
import { Work } from '@/types';
import { mockWorks, mockWorksByCategory, trendingWorks, latestWorks } from '@/data/mockWorks';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { recommendationEngine, simulateUserBehaviors } from '@/services/recommendationService';

export default function Home() {
  const [works, setWorks] = useState<Work[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('trending');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const { isAuthenticated, user: supabaseUser } = useSimpleAuth();

  const ITEMS_PER_PAGE = 16;

  // Initialize recommendation system
  useEffect(() => {
    // Simulate user behavior data (development environment only)
    if (process.env.NODE_ENV === 'development') {
      simulateUserBehaviors();
    }
  }, []);

  const fetchWorks = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      // 使用模拟数据，根据选择的分类过滤
      let allFilteredWorks: Work[] = [];

      switch (selectedCategory) {
        case 'trending':
          allFilteredWorks = recommendationEngine.getTrendingRecommendations(mockWorks, 50);
          break;
        case 'recommended':
          // 个性化推荐
          if (isAuthenticated && supabaseUser?.id) {
            allFilteredWorks = recommendationEngine.getPersonalizedRecommendations(
              supabaseUser.id,
              mockWorks,
              50
            );
          } else {
            allFilteredWorks = trendingWorks;
          }
          break;
        case 'all':
          allFilteredWorks = latestWorks;
          break;
        default:
          allFilteredWorks = mockWorksByCategory[selectedCategory as keyof typeof mockWorksByCategory] || [];
          break;
      }

      // 计算分页
      const startIndex = (page - 1) * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const pageWorks = allFilteredWorks.slice(startIndex, endIndex);

      // 检查是否还有更多数据
      const hasMoreData = endIndex < allFilteredWorks.length;
      setHasMore(hasMoreData);

      if (append) {
        setWorks(prev => [...prev, ...pageWorks]);
      } else {
        setWorks(pageWorks);
      }

      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching works:', error);
      // 使用默认数据作为后备
      if (page === 1) {
        setWorks(mockWorks.slice(0, ITEMS_PER_PAGE));
        setHasMore(mockWorks.length > ITEMS_PER_PAGE);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [selectedCategory]);

  useEffect(() => {
    setCurrentPage(1);
    setHasMore(true);
    fetchWorks(1, false);
  }, [fetchWorks]);

  const handleLoadMore = () => {
    if (hasMore && !loadingMore) {
      fetchWorks(currentPage + 1, true);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex">
          <CategorySidebar
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
          />
          <main className="flex-1 p-8">
            <div className="mb-8">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-2 animate-pulse"></div>
              <div className="h-6 bg-gray-200 rounded w-1/3 mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(9)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg overflow-hidden animate-pulse">
                  <div className="h-48 bg-gray-200"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-full mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
              ))}
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="flex">
        {/* Category Sidebar */}
        <CategorySidebar
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
        />

        {/* Main Content */}
        <main className="flex-1 p-8">
          {/* Top Banner Ad */}
          <AdBanner position="top" />
          
          {/* Content Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Home</h1>
            <h2 className="text-xl font-semibold text-gray-700 mb-2">
              {selectedCategory === 'trending'
                ? 'Recommended for you'
                : `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} Works`}
            </h2>
            <p className="text-gray-600">
              Discover amazing articles, videos, and audio content from creators around the world
            </p>
          </div>

          {/* Works Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {works.length === 0 ? (
              <div className="col-span-full text-center py-16">
                <div className="text-gray-400 text-6xl mb-4">📝</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No content found</h3>
                <p className="text-gray-600">
                  Try selecting a different category or check back later.
                </p>
              </div>
            ) : (
              works.map((work) => (
                <WorkCard
                  key={work.id}
                  work={work}
                  layout="vertical"
                  showImage={true}
                  showAuthor={true}
                  showStats={true}
                  showInteractions={false}
                />
              ))
            )}
          </div>

          {/* Load More Button */}
          {works.length > 0 && hasMore && (
            <div className="text-center mt-12">
              <button
                onClick={handleLoadMore}
                disabled={loadingMore}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center mx-auto"
              >
                {loadingMore ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Loading...
                  </>
                ) : (
                  'Discover More Content'
                )}
              </button>
            </div>
          )}

          {/* End of content message */}
          {works.length > 0 && !hasMore && (
            <div className="text-center mt-12">
              <p className="text-gray-500">You've discovered all available content in this category</p>
            </div>
          )}
        </main>
        
        {/* Right Sidebar Ad */}
        <div className="w-80 p-4 hidden xl:block">
          <div className="sticky top-24">
            <AdBanner position="sidebar" />
          </div>
        </div>
      </div>
    </div>
  );
}