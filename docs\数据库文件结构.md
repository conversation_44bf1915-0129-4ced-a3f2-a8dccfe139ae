# 🗂️ Newzora 数据库文件结构

## 📁 文件组织结构

### 主站前台 (Web Frontend)
```
Newzora/
├── Backend/                    # 主站后端
│   ├── config/database.js      # 数据库配置
│   ├── scripts/init-db.sql     # 初始化脚本
│   └── .env                    # 环境变量
├── scripts/
│   ├── web-database-manager.js # 主站数据库管理工具
│   └── database-manager.js     # 协调管理工具
└── backups/                    # 主站数据库备份
```

### 后台管理系统 (Admin Backend)
```
NewzoraAdmin/
├── Backend/                    # 管理后台后端
│   ├── config/database.js      # 数据库配置
│   ├── scripts/
│   │   ├── init-db.sql         # 初始化脚本
│   │   └── initDatabase.js     # Node.js初始化
│   └── .env                    # 环境变量
├── scripts/
│   └── database-manager.js     # 管理后台数据库工具
├── backups/                    # 管理后台数据库备份
└── package.json                # 管理后台项目配置
```

## 🚀 使用方法

### 主站前台数据库管理
```bash
# 在主项目根目录
npm run db:create:web           # 创建主站数据库
npm run db:init:web             # 初始化主站数据库
npm run db:backup:web           # 备份主站数据库
npm run db:status:web           # 检查主站数据库状态

# 或直接使用脚本
node scripts/web-database-manager.js setup
```

### 后台管理系统数据库管理
```bash
# 在主项目根目录
npm run db:create:admin         # 创建管理后台数据库
npm run db:init:admin           # 初始化管理后台数据库
npm run db:backup:admin         # 备份管理后台数据库
npm run db:status:admin         # 检查管理后台数据库状态

# 或在NewzoraAdmin目录
cd NewzoraAdmin
npm run db:setup

# 或直接使用脚本
node NewzoraAdmin/scripts/database-manager.js setup
```

### 协调管理（同时管理两个数据库）
```bash
# 在主项目根目录
npm run db:setup               # 设置所有数据库
node scripts/database-manager.js setup
```

## 📊 数据库信息

| 系统 | 数据库名称 | 密码 | 端口 | 管理工具路径 |
|------|------------|------|------|-------------|
| 主站前台 | PostgreSQL-newzora_web | wasd080980! | 5432 | scripts/web-database-manager.js |
| 后台管理 | PostgreSQL-newzora_admin | QWasd080980! | 5433 | NewzoraAdmin/scripts/database-manager.js |

## 🔧 开发工作流

### 1. 初始设置
```bash
# 安装所有依赖
npm run install:all

# 设置数据库
npm run db:setup

# 验证配置
npm run db:verify
```

### 2. 日常开发
```bash
# 启动主站前台
npm run dev

# 启动后台管理系统
npm run dev:admin

# 启动所有服务
npm run dev:all
```

### 3. 数据库维护
```bash
# 备份数据库
npm run db:backup:web
npm run db:backup:admin

# 检查状态
npm run db:status:web
npm run db:status:admin
```

## ✅ 优势

1. **清晰分离**: 前台和后台数据库管理工具完全分离
2. **独立维护**: 每个系统可以独立管理其数据库
3. **统一协调**: 提供协调工具管理整体数据库架构
4. **备份隔离**: 各自的备份目录，避免混淆
5. **配置独立**: 各自的环境变量和配置文件