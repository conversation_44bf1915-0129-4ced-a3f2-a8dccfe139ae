'use client';

import React, { useState, useEffect } from 'react';
import { Search, Filter, Download, Plus, Shield, UserCheck, Award, X } from 'lucide-react';
import DataTable from '@/components/admin/common/DataTable';
import { userService } from '@/services/userService';
import { AdminUser, DataTableColumn } from '@/types/admin';

const UsersPage: React.FC = () => {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [verificationFilter, setVerificationFilter] = useState(''); // Added verification status filter
  const [pagination, setPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 20
  });
  const [totalPages, setTotalPages] = useState(0);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [newUser, setNewUser] = useState({
    username: '',
    email: '',
    displayName: '',
    role: 'user' as 'user' | 'admin' | 'moderator'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const columns: DataTableColumn<AdminUser>[] = [
    {
      key: 'username',
      title: 'User Info',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-medium text-sm shadow-md flex-shrink-0">
            {record.display_name.charAt(0).toUpperCase()}
          </div>
          <div className="ml-3 min-w-0">
            <div className="text-sm font-semibold text-gray-900 truncate">{record.display_name}</div>
            <div className="text-xs text-gray-600 truncate">{record.email}</div>
            <div className="text-xs text-gray-500 truncate">@{record.username}</div>
          </div>
        </div>
      )
    },
    {
      key: 'role',
      title: 'Role',
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-3 py-2 text-sm font-semibold rounded-lg shadow-sm border ${
          value === 'super_admin' ? 'bg-purple-100 text-purple-800 border-purple-200' :
          value === 'admin' ? 'bg-blue-100 text-blue-800 border-blue-200' :
          value === 'moderator' ? 'bg-green-100 text-green-800 border-green-200' :
          'bg-gray-100 text-gray-800 border-gray-200'
        }`}>
          {value === 'super_admin' ? '👑 Super Admin' :
           value === 'admin' ? '🛡️ Admin' :
           value === 'moderator' ? '⚖️ Moderator' : '👤 User'}
        </span>
      )
    },
    {
      key: 'is_active',
      title: 'Status',
      sortable: true,
      render: (value) => (
        <span className={`inline-flex items-center px-3 py-2 text-sm font-semibold rounded-lg shadow-sm border ${
          value ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200'
        }`}>
          {value ? '✅ Active' : '❌ Inactive'}
        </span>
      )
    },
    {
      key: 'email_verified', // Changed to use existing field
      title: 'Verification',
      sortable: true,
      render: (_, record) => {
        // Determine verification status based on user attributes
        const isVerified = record.email_verified || record.is_verified;
        const isPremium = record.is_premium;
        const isExpert = record.is_expert;
        
        return (
          <div className="flex flex-col space-y-1">
            {isVerified && (
              <span className="inline-flex items-center px-1.5 py-0.5 text-xs font-semibold rounded bg-green-100 text-green-800 border border-green-200 shadow-sm">
                <UserCheck className="w-2.5 h-2.5 mr-0.5" />
                Email
              </span>
            )}
            {isPremium && (
              <span className="inline-flex items-center px-1.5 py-0.5 text-xs font-semibold rounded bg-blue-100 text-blue-800 border border-blue-200 shadow-sm">
                <Award className="w-2.5 h-2.5 mr-0.5" />
                Premium
              </span>
            )}
            {isExpert && (
              <span className="inline-flex items-center px-1.5 py-0.5 text-xs font-semibold rounded bg-purple-100 text-purple-800 border border-purple-200 shadow-sm">
                <Shield className="w-2.5 h-2.5 mr-0.5" />
                Expert
              </span>
            )}
            {!isVerified && !isPremium && !isExpert && (
              <span className="inline-flex px-1.5 py-0.5 text-xs font-semibold rounded bg-gray-100 text-gray-800 border border-gray-200 shadow-sm">
                Unverified
              </span>
            )}
          </div>
        );
      }
    },
    {
      key: 'stats',
      title: 'Statistics',
      render: (stats) => (
        <div className="text-sm text-gray-900">
          <div>Articles: {stats?.articles || 0}</div>
          <div>Comments: {stats?.comments || 0}</div>
        </div>
      )
    },
    {
      key: 'created_at',
      title: 'Registered',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString('en-US')
    },
    {
      key: 'last_login_at',
      title: 'Last Login',
      sortable: true,
      render: (value) => value ? new Date(value).toLocaleDateString('en-US') : 'Never logged in'
    },
    {
      key: 'id',
      title: 'Actions',
      render: (_, record) => (
        <div className="flex flex-col space-y-1">
          <select
            value={record.role}
            onChange={(e) => handleRoleChange(record.id, e.target.value)}
            className="text-sm border rounded px-1 py-0.5 w-full"
          >
            <option value="user">User</option>
            <option value="moderator">Mod</option>
            <option value="admin">Admin</option>
            <option value="super_admin">Super</option>
          </select>
          <button 
            onClick={(e) => {
              e.preventDefault();
              handleUserStatusToggle(record.id, record.is_active);
            }}
            className={`text-sm px-2 py-0.5 rounded whitespace-nowrap w-full ${
              record.is_active
                ? 'bg-red-100 text-red-600 hover:bg-red-200'
                : 'bg-green-100 text-green-600 hover:bg-green-200'
            }`}
          >
            {record.is_active ? 'Deactivate' : 'Activate'}
          </button>
        </div>
      )
    }
  ];

  const fetchUsers = async () => {
    try {
      setLoading(true);
      // 使用模拟数据，因为userService可能不存在
      const mockUsers: AdminUser[] = [];
      for (let i = 1; i <= 50; i++) {
        mockUsers.push({
          id: i.toString(),
          username: `user${i}`,
          email: `user${i}@example.com`,
          display_name: `User ${i}`,
          role: ['user', 'admin', 'moderator'][Math.floor(Math.random() * 3)] as any,
          is_active: Math.random() > 0.2,
          is_verified: Math.random() > 0.3,
          is_premium: Math.random() > 0.7,
          is_expert: Math.random() > 0.8,
          email_verified: Math.random() > 0.4,
          last_login_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
          created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
          stats: {
            articles: Math.floor(Math.random() * 50),
            comments: Math.floor(Math.random() * 200),
            followers: Math.floor(Math.random() * 1000),
            following: Math.floor(Math.random() * 500),
            views: Math.floor(Math.random() * 10000),
            likes: Math.floor(Math.random() * 5000)
          }
        });
      }

      // 应用过滤器
      let filteredUsers = mockUsers;

      if (searchQuery) {
        filteredUsers = filteredUsers.filter(user =>
          user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.display_name.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      if (roleFilter) {
        filteredUsers = filteredUsers.filter(user => user.role === roleFilter);
      }

      if (statusFilter) {
        const isActive = statusFilter === 'active';
        filteredUsers = filteredUsers.filter(user => user.is_active === isActive);
      }

      setUsers(filteredUsers);
      setPagination(prev => ({
        ...prev,
        total: filteredUsers.length
      }));
    } catch (error) {
      console.error('Failed to fetch user list:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [pagination.current, searchQuery, roleFilter, statusFilter]);

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newUser.username || !newUser.email) {
      alert('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);
    try {
      // 模拟API调用 - 实际项目中应该调用真实的API
      const newUserData: AdminUser = {
        id: Date.now().toString(),
        username: newUser.username,
        email: newUser.email,
        display_name: newUser.displayName || newUser.username,
        role: newUser.role as any,
        is_active: true,
        is_verified: false,
        is_premium: false,
        is_expert: false,
        email_verified: false,
        last_login_at: '',
        created_at: new Date().toISOString(),
        stats: {
          articles: 0,
          comments: 0,
          followers: 0,
          following: 0,
          views: 0,
          likes: 0
        }
      };

      // 添加到用户列表
      setUsers(prev => [newUserData, ...prev]);
      setPagination(prev => ({ ...prev, total: prev.total + 1 }));

      // 重置表单
      setNewUser({
        username: '',
        email: '',
        displayName: '',
        role: 'user'
      });
      setShowAddUserModal(false);

      alert('User added successfully!');
    } catch (error) {
      console.error('Failed to add user:', error);
      alert('Failed to add user. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchUsers();
  };

  const handleBulkAction = async (action: string) => {
    if (selectedUsers.length === 0) {
      alert('Please select users first');
      return;
    }
    
    if (!confirm(`Are you sure you want to ${action} ${selectedUsers.length} users?`)) {
      return;
    }
    
    try {
      await userService.bulkUpdateUsers(selectedUsers, action);
      
      // Update local data
      if (action === 'activate') {
        setUsers(prev => prev.map(user =>
          selectedUsers.includes(user.id) ? { ...user, is_active: true } : user
        ));
      } else if (action === 'deactivate') {
        setUsers(prev => prev.map(user => 
          selectedUsers.includes(user.id) ? { ...user, is_active: false } : user
        ));
      }
      
      setSelectedUsers([]);
      alert(`Bulk operation successful, affected ${selectedUsers.length} users`);
    } catch (error) {
      console.error('Bulk operation failed:', error);
      alert('Bulk operation failed, please try again');
    }
  };

  const handleUserStatusToggle = async (userId: string, currentStatus: boolean) => {
    try {
      await userService.updateUserStatus(userId, !currentStatus);
      // Update local data
      setUsers(prev => prev.map(user =>
        user.id === userId ? { ...user, is_active: !currentStatus } : user
      ));
      alert(`User status has been ${!currentStatus ? 'activated' : 'deactivated'}`);
    } catch (error) {
      console.error('Failed to update user status:', error);
      alert('Operation failed, please try again');
    }
  };

  const handleRoleChange = async (userId: string, newRole: string) => {
    try {
      await userService.updateUserRole(userId, newRole);
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, role: newRole as any } : user
      ));
      alert(`User role has been updated to ${newRole}`);
    } catch (error) {
      console.error('Failed to update user role:', error);
      alert('Operation failed, please try again');
    }
  };

  // Added function to handle verification status changes
  const handleVerificationChange = async (userId: string, verificationType: string, status: boolean) => {
    try {
      // In real applications, this should call API to update user verification status
      console.log(`Update user ${userId} ${verificationType} verification status to ${status}`);

      // Simulate updating local data
      setUsers(prev => prev.map(user => {
        if (user.id === userId) {
          const updatedUser = { ...user };
          switch (verificationType) {
            case 'email':
              updatedUser.email_verified = status;
              break;
            case 'premium':
              updatedUser.is_premium = status;
              break;
            case 'expert':
              updatedUser.is_expert = status;
              break;
          }
          return updatedUser;
        }
        return user;
      }));
      
      alert('Verification status updated successfully');
    } catch (error) {
      console.error('Failed to update verification status:', error);
      alert('Operation failed, please try again');
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
        <div className="flex space-x-3">
          <button className="btn-secondary flex items-center">
            <Download className="w-4 h-4 mr-2" />
            Export Data
          </button>
          <button
            onClick={() => setShowAddUserModal(true)}
            className="btn-primary flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add User
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search username, email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="form-input pl-10 w-full"
              />
            </div>
          </div>
          <div className="flex flex-wrap gap-4">
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="form-select w-40"
            >
              <option value="">All Roles</option>
              <option value="user">User</option>
              <option value="moderator">Moderator</option>
              <option value="admin">Admin</option>
              <option value="super_admin">Super Admin</option>
            </select>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="form-select w-40"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            {/* Added verification status filter */}
            <select
              value={verificationFilter}
              onChange={(e) => setVerificationFilter(e.target.value)}
              className="form-select w-40"
            >
              <option value="">All Verification</option>
              <option value="verified">Email Verified</option>
              <option value="premium">Premium User</option>
              <option value="expert">Expert Verified</option>
              <option value="unverified">Unverified</option>
            </select>
            <button type="submit" className="btn-primary flex items-center whitespace-nowrap">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </button>
          </div>
        </form>
      </div>

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              Selected {selectedUsers.length} users
            </span>
            <div className="flex space-x-2">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  handleBulkAction('activate');
                }}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                Bulk Activate
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  handleBulkAction('deactivate');
                }}
                className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
              >
                Bulk Deactivate
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  if (confirm('Are you sure you want to delete selected users? This action cannot be undone!')) {
                    handleBulkAction('delete');
                  }
                }}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Bulk Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* User List */}
      <DataTable
        data={users}
        columns={columns}
        loading={loading}
        pagination={{
          current: pagination.current,
          total: pagination.total,
          pageSize: pagination.pageSize,
          onChange: (page) => setPagination(prev => ({ ...prev, current: page }))
        }}
        rowSelection={{
          selectedRowKeys: selectedUsers,
          onChange: setSelectedUsers
        }}
      />

      {/* Add User Modal */}
      {showAddUserModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Add New User</h3>
              <button
                onClick={() => setShowAddUserModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleAddUser} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Username *
                </label>
                <input
                  type="text"
                  value={newUser.username}
                  onChange={(e) => setNewUser(prev => ({ ...prev, username: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Display Name
                </label>
                <input
                  type="text"
                  value={newUser.displayName}
                  onChange={(e) => setNewUser(prev => ({ ...prev, displayName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Role
                </label>
                <select
                  value={newUser.role}
                  onChange={(e) => setNewUser(prev => ({ ...prev, role: e.target.value as 'user' | 'admin' | 'moderator' }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="user">User</option>
                  <option value="moderator">Moderator</option>
                  <option value="admin">Admin</option>
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddUserModal(false)}
                  className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSubmitting ? 'Adding...' : 'Add User'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default UsersPage;