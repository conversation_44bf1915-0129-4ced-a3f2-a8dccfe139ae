'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';

interface SafeRenderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error) => void;
  className?: string;
}

/**
 * SafeRender 组件 - 防止DOM操作错误导致的React崩溃
 * 特别针对 insertBefore, removeChild 等DOM操作错误
 */
export default function SafeRender({ 
  children, 
  fallback = null, 
  onError,
  className 
}: SafeRenderProps) {
  const [hasError, setHasError] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const mountedRef = useRef(false);

  useEffect(() => {
    mountedRef.current = true;
    setIsClient(true);
    
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const handleError = useCallback((error: Error) => {
    // Filter out safe DOM errors
    if (error.message?.includes('insertBefore') ||
        error.message?.includes('removeChild') ||
        error.message?.includes('Node') ||
        error.message?.includes('Failed to execute')) {
      console.warn('SafeRender filtered DOM error:', error.message);
      return;
    }

    console.error('SafeRender caught error:', error);
    if (mountedRef.current) {
      setHasError(true);
      onError?.(error);
    }
  }, [onError]);

  // Error boundary effect
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason instanceof Error) {
        handleError(event.reason);
      }
    };

    const handleErrorEvent = (event: ErrorEvent) => {
      if (event.error instanceof Error) {
        handleError(event.error);
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleErrorEvent);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleErrorEvent);
    };
  }, [handleError]);

  if (hasError) {
    return (
      <div className={className} ref={containerRef}>
        {fallback || (
          <div className="p-4 text-center text-gray-500">
            <p>Something went wrong. Please refresh the page.</p>
          </div>
        )}
      </div>
    );
  }

  if (!isClient) {
    return (
      <div className={className} suppressHydrationWarning>
        {fallback || children}
      </div>
    );
  }

  return (
    <div className={className} ref={containerRef}>
      {children}
    </div>
  );
}

/**
 * Higher-order component version of SafeRender
 */
export function withSafeRender<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode
) {
  return function SafeComponent(props: P) {
    return (
      <SafeRender fallback={fallback}>
        <Component {...props} />
      </SafeRender>
    );
  };
}
