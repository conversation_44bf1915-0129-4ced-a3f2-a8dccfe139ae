'use client';

import React, { useState, useEffect } from 'react';
import { 
  SparklesIcon, 
  LightBulbIcon, 
  DocumentTextIcon, 
  TagIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ArrowPathIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface AICreationAssistantProps {
  className?: string;
  contentType?: 'article' | 'video' | 'audio' | 'live';
  currentTitle?: string;
  currentContent?: string;
  onSuggestionApply?: (type: 'title' | 'content' | 'tags' | 'outline', suggestion: string) => void;
}

interface AISuggestion {
  id: string;
  type: 'title' | 'content' | 'tags' | 'outline' | 'seo';
  content: string;
  confidence: number;
  applied?: boolean;
}

const AICreationAssistant: React.FC<AICreationAssistantProps> = ({
  className = '',
  contentType = 'article',
  currentTitle = '',
  currentContent = '',
  onSuggestionApply
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
  const [activeTab, setActiveTab] = useState<'suggestions' | 'analysis'>('suggestions');

  // Mock AI suggestions generation
  const generateSuggestions = async () => {
    setIsGenerating(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const mockSuggestions: AISuggestion[] = [
      {
        id: 'title_1',
        type: 'title',
        content: contentType === 'video' 
          ? '10 Essential Tips for Creating Engaging Video Content'
          : contentType === 'audio'
          ? 'The Ultimate Guide to Podcast Production'
          : 'How to Write Content That Converts: A Complete Guide',
        confidence: 0.92
      },
      {
        id: 'outline_1',
        type: 'outline',
        content: contentType === 'video'
          ? '1. Planning Your Video\n2. Equipment Setup\n3. Filming Techniques\n4. Post-Production Tips\n5. Publishing Strategy'
          : '1. Introduction\n2. Main Points\n3. Supporting Evidence\n4. Practical Examples\n5. Conclusion & Call to Action',
        confidence: 0.88
      },
      {
        id: 'tags_1',
        type: 'tags',
        content: contentType === 'video'
          ? 'video creation, content marketing, digital media, tutorial, tips'
          : contentType === 'audio'
          ? 'podcast, audio content, broadcasting, storytelling'
          : 'content writing, marketing, SEO, engagement, strategy',
        confidence: 0.95
      },
      {
        id: 'seo_1',
        type: 'seo',
        content: 'Focus keywords: content creation, engagement tips, digital marketing. Consider adding more specific long-tail keywords for better ranking.',
        confidence: 0.85
      }
    ];
    
    setSuggestions(mockSuggestions);
    setIsGenerating(false);
  };

  const applySuggestion = (suggestion: AISuggestion) => {
    if (onSuggestionApply) {
      onSuggestionApply(suggestion.type as any, suggestion.content);
    }
    
    // Mark as applied
    setSuggestions(prev => 
      prev.map(s => 
        s.id === suggestion.id ? { ...s, applied: true } : s
      )
    );
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600 bg-green-100';
    if (confidence >= 0.8) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'title': return <DocumentTextIcon className="w-4 h-4" />;
      case 'outline': return <LightBulbIcon className="w-4 h-4" />;
      case 'tags': return <TagIcon className="w-4 h-4" />;
      default: return <SparklesIcon className="w-4 h-4" />;
    }
  };

  useEffect(() => {
    if (currentTitle || currentContent) {
      generateSuggestions();
    }
  }, [currentTitle, currentContent, contentType]);

  return (
    <div className={`bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50">
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="w-full flex items-center justify-between text-left hover:bg-white/50 rounded-lg p-2 transition-colors"
        >
          <h3 className="text-base font-semibold text-gray-800 flex items-center">
            <SparklesIcon className="w-5 h-5 mr-2 text-purple-500" />
            AI Creation Assistant
            <span className="ml-2 text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
              Beta
            </span>
          </h3>
          {isCollapsed ? (
            <ChevronDownIcon className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronUpIcon className="w-5 h-5 text-gray-500" />
          )}
        </button>
      </div>

      {/* Content */}
      {!isCollapsed && (
        <div className="p-4">
          {/* Tab Navigation */}
          <div className="flex space-x-1 mb-4 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('suggestions')}
              className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'suggestions'
                  ? 'bg-white text-purple-700 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Suggestions
            </button>
            <button
              onClick={() => setActiveTab('analysis')}
              className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'analysis'
                  ? 'bg-white text-purple-700 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Analysis
            </button>
          </div>

          {/* Generate Button */}
          <button
            onClick={generateSuggestions}
            disabled={isGenerating}
            className="w-full mb-4 py-2 px-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
          >
            {isGenerating ? (
              <>
                <ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <SparklesIcon className="w-4 h-4 mr-2" />
                Generate AI Suggestions
              </>
            )}
          </button>

          {/* Suggestions Tab */}
          {activeTab === 'suggestions' && (
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {suggestions.length === 0 && !isGenerating && (
                <div className="text-center py-6 text-gray-500">
                  <SparklesIcon className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">Click "Generate AI Suggestions" to get started</p>
                </div>
              )}

              {suggestions.map((suggestion) => (
                <div key={suggestion.id} className="border border-gray-200 rounded-lg p-3 hover:border-purple-200 transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <div className="text-purple-600">
                        {getSuggestionIcon(suggestion.type)}
                      </div>
                      <span className="text-sm font-medium text-gray-800 capitalize">
                        {suggestion.type}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getConfidenceColor(suggestion.confidence)}`}>
                        {Math.round(suggestion.confidence * 100)}%
                      </span>
                    </div>
                    <div className="flex space-x-1">
                      {suggestion.applied ? (
                        <CheckIcon className="w-4 h-4 text-green-600" />
                      ) : (
                        <button
                          onClick={() => applySuggestion(suggestion)}
                          className="p-1 hover:bg-purple-100 rounded transition-colors"
                          title="Apply suggestion"
                        >
                          <CheckIcon className="w-4 h-4 text-gray-500 hover:text-purple-600" />
                        </button>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-gray-700 whitespace-pre-line">
                    {suggestion.content}
                  </p>
                </div>
              ))}
            </div>
          )}

          {/* Analysis Tab */}
          {activeTab === 'analysis' && (
            <div className="space-y-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="text-sm font-semibold text-blue-800 mb-2">Content Analysis</h4>
                <div className="space-y-2 text-sm text-blue-700">
                  <div className="flex justify-between">
                    <span>Readability Score:</span>
                    <span className="font-medium">Good (78/100)</span>
                  </div>
                  <div className="flex justify-between">
                    <span>SEO Potential:</span>
                    <span className="font-medium">High</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Engagement Prediction:</span>
                    <span className="font-medium">85%</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-yellow-50 rounded-lg p-4">
                <h4 className="text-sm font-semibold text-yellow-800 mb-2">Recommendations</h4>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Add more subheadings for better structure</li>
                  <li>• Include relevant keywords in the first paragraph</li>
                  <li>• Consider adding a call-to-action at the end</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AICreationAssistant;
