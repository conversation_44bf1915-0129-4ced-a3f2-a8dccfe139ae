'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';

interface SafeRenderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error) => void;
  className?: string;
}

/**
 * SafeRender 组件 - 防止DOM操作错误导致的React崩溃
 * 特别针对 insertBefore, removeChild 等DOM操作错误
 */
export default function SafeRender({ 
  children, 
  fallback = null, 
  onError,
  className 
}: SafeRenderProps) {
  const [hasError, setHasError] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const mountedRef = useRef(false);

  useEffect(() => {
    mountedRef.current = true;
    setIsClient(true);
    
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const handleError = useCallback((error: Error) => {
    // Filter out safe DOM errors
    if (error.message?.includes('insertBefore') ||
        error.message?.includes('removeChild') ||
        error.message?.includes('Node') ||
        error.message?.includes('Failed to execute')) {
      console.warn('SafeRender filtered DOM error:', error.message);
      return;
    }

    console.error('SafeRender caught error:', error);
    if (mountedRef.current) {
      setHasError(true);
      onError?.(error);
    }
  }, [onError]);

  // Error boundary effect
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason instanceof Error) {
        handleError(event.reason);
      }
    };

    const handleError = (event: ErrorEvent) => {
      if (event.error instanceof Error) {
        handleError(event.error);
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, [handleError]);

  // DOM mutation observer to catch DOM errors
  useEffect(() => {
    if (!isClient || !containerRef.current) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        try {
          // Check for invalid DOM operations
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              // Validate that the node is properly attached
              if (!element.parentNode || !document.contains(element)) {
                console.warn('SafeRender detected orphaned node:', element);
              }
            }
          });
        } catch (error) {
          console.warn('SafeRender mutation observer error:', error);
        }
      });
    });

    observer.observe(containerRef.current, {
      childList: true,
      subtree: true,
      attributes: false
    });

    return () => observer.disconnect();
  }, [isClient]);

  if (hasError) {
    return (
      <div className={className} ref={containerRef}>
        {fallback || (
          <div className="p-4 text-center text-gray-500">
            <p>Something went wrong. Please refresh the page.</p>
          </div>
        )}
      </div>
    );
  }

  if (!isClient) {
    return (
      <div className={className} suppressHydrationWarning>
        {fallback || children}
      </div>
    );
  }

  return (
    <div className={className} ref={containerRef}>
      {children}
    </div>
  );
}

/**
 * Higher-order component version of SafeRender
 */
export function withSafeRender<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode
) {
  return function SafeComponent(props: P) {
    return (
      <SafeRender fallback={fallback}>
        <Component {...props} />
      </SafeRender>
    );
  };
}

/**
 * Hook for safe DOM operations
 */
export function useSafeDOM() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const safeOperation = useCallback((operation: () => void) => {
    if (!isClient) return;

    try {
      operation();
    } catch (error) {
      if (error instanceof Error) {
        if (error.message?.includes('insertBefore') ||
            error.message?.includes('removeChild') ||
            error.message?.includes('Node')) {
          console.warn('Safe DOM operation filtered error:', error.message);
          return;
        }
      }
      throw error;
    }
  }, [isClient]);

  return { safeOperation, isClient };
}
