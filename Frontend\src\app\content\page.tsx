'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useData } from '@/contexts/DataContext';
import Header from '@/components/Header';
import AuthGuard from '@/components/AuthGuard';
import Link from 'next/link';

type TabType = 'published' | 'drafts' | 'analytics';

const mockArticles = [
  {
    id: 1,
    title: 'The Future of AI in Everyday Life',
    status: 'published',
    type: 'article',
    publishedAt: '2024-01-15',
    updatedAt: '2024-01-14',
    views: 1250,
    likes: 42,
    category: 'Technology',
  },
  {
    id: 2,
    title: 'Cybersecurity Best Practices',
    status: 'draft',
    type: 'article',
    updatedAt: '2024-01-14',
    category: 'Technology',
  },
  {
    id: 3,
    title: 'Travel Guide to Southeast Asia',
    status: 'published',
    type: 'video',
    publishedAt: '2024-01-12',
    updatedAt: '2024-01-11',
    views: 890,
    likes: 38,
    category: 'Travel',
  },
  {
    id: 4,
    title: 'Meditation Music Collection',
    status: 'draft',
    type: 'audio',
    updatedAt: '2024-01-10',
    category: 'Lifestyle',
  },
];

function ContentManagementContent() {
  const searchParams = useSearchParams();
  const { user, isAuthenticated } = useSimpleAuth();
  const { articles, updateArticle, deleteArticle } = useData();
  const [activeTab, setActiveTab] = useState<TabType>('published');
  const [mounted, setMounted] = useState(false);
  const [userContent, setUserContent] = useState<any[]>([]);

  useEffect(() => {
    setMounted(true);
    // 加载用户创建的内容
    loadUserContent();

    // 检查URL参数设置默认标签
    const tab = searchParams.get('tab');
    if (tab === 'drafts') {
      setActiveTab('drafts');
    }
  }, [searchParams]);

  const loadUserContent = () => {
    try {
      const savedContent = localStorage.getItem('userContent');
      if (savedContent) {
        const content = JSON.parse(savedContent);
        setUserContent(content);
      }
    } catch (error) {
      console.error('Error loading user content:', error);
    }
  };

  if (!mounted) {
    return null;
  }

  const handleDeleteDraft = (id: number) => {
    if (confirm('Are you sure you want to delete this draft? This action cannot be undone.')) {
      console.log('Deleting draft with ID:', id, 'Type:', typeof id);

      // 检查是否是用户创建的内容（确保类型匹配）
      const isUserContent = userContent.some(content => Number(content.id) === Number(id));

      if (isUserContent) {
        console.log('Deleting from user content');
        // 从用户内容中删除
        const updatedContent = userContent.filter(content => Number(content.id) !== Number(id));
        setUserContent(updatedContent);
        localStorage.setItem('userContent', JSON.stringify(updatedContent));
      } else {
        console.log('Deleting from mock data');
        // 从mock数据中删除（通过DataContext）
        deleteArticle(id);
      }
    }
  };

  const handleUnpublish = (id: number) => {
    if (confirm('Are you sure you want to unpublish this article? It will be moved to drafts.')) {
      console.log('Unpublishing article with ID:', id, 'Type:', typeof id);

      // 检查是否是用户创建的内容（确保类型匹配）
      const isUserContent = userContent.some(content => Number(content.id) === Number(id));

      if (isUserContent) {
        console.log('Unpublishing user content');
        // 更新用户内容状态
        const updatedContent = userContent.map(content =>
          Number(content.id) === Number(id) ? { ...content, status: 'draft' } : content
        );
        setUserContent(updatedContent);
        localStorage.setItem('userContent', JSON.stringify(updatedContent));
      } else {
        console.log('Unpublishing mock data');
        // 更新mock数据（通过DataContext）
        updateArticle(id, { status: 'draft' });
      }
    }
  };

  const handlePublish = (id: number) => {
    if (confirm('Are you sure you want to publish this draft?')) {
      console.log('Publishing draft with ID:', id, 'Type:', typeof id);

      // 检查是否是用户创建的内容（确保类型匹配）
      const isUserContent = userContent.some(content => Number(content.id) === Number(id));

      if (isUserContent) {
        console.log('Publishing user content');
        // 更新用户内容状态为已发布
        const updatedContent = userContent.map(content =>
          Number(content.id) === Number(id) ? {
            ...content,
            status: 'published',
            publishedAt: new Date().toISOString()
          } : content
        );
        setUserContent(updatedContent);
        localStorage.setItem('userContent', JSON.stringify(updatedContent));
      } else {
        console.log('Publishing mock data');
        // 更新mock数据（通过DataContext）
        updateArticle(id, { status: 'published' });
      }
    }
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to permanently delete this article? This action cannot be undone.')) {
      console.log('Deleting article with ID:', id, 'Type:', typeof id);

      // 检查是否是用户创建的内容（确保类型匹配）
      const isUserContent = userContent.some(content => Number(content.id) === Number(id));

      if (isUserContent) {
        console.log('Deleting from user content');
        // 从用户内容中删除
        const updatedContent = userContent.filter(content => Number(content.id) !== Number(id));
        setUserContent(updatedContent);
        localStorage.setItem('userContent', JSON.stringify(updatedContent));
      } else {
        console.log('Deleting from mock data');
        // 从mock数据中删除（通过DataContext）
        deleteArticle(id);
      }
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Content Management</h2>
          <p className="text-gray-600 mb-6">Please log in to manage your content</p>
          <Link
            href="/auth/login"
            className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Login Now
          </Link>
        </div>
      </div>
    );
  }

  // 合并mock数据和用户创建的内容
  const allContent = [...mockArticles, ...userContent];

  const filteredArticles = allContent.filter((article) => {
    if (activeTab === 'published') return article.status === 'published';
    if (activeTab === 'drafts') return article.status === 'draft';
    return true;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Content</h1>
              <p className="mt-2 text-gray-600">Manage your published articles and drafts</p>
            </div>
            <Link
              href="/create"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create New Article
            </Link>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveTab('published')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'published'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Published ({allContent.filter((a) => a.status === 'published').length})
              </button>
              <button
                onClick={() => setActiveTab('drafts')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'drafts'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Drafts ({allContent.filter((a) => a.status === 'draft').length})
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'analytics'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Analytics
              </button>
            </nav>
          </div>
        </div>

        {/* Content List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {activeTab === 'analytics' ? (
            <div className="p-8 text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Analytics Dashboard</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-6 rounded-lg">
                  <h4 className="text-2xl font-bold text-blue-600">1,250</h4>
                  <p className="text-sm text-gray-600">Total Views</p>
                </div>
                <div className="bg-green-50 p-6 rounded-lg">
                  <h4 className="text-2xl font-bold text-green-600">42</h4>
                  <p className="text-sm text-gray-600">Total Likes</p>
                </div>
                <div className="bg-purple-50 p-6 rounded-lg">
                  <h4 className="text-2xl font-bold text-purple-600">3</h4>
                  <p className="text-sm text-gray-600">Published Articles</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredArticles.length === 0 ? (
                <div className="p-8 text-center">
                  <p className="text-gray-500">No {activeTab} found.</p>
                  <Link
                    href="/create"
                    className="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Your First Article
                  </Link>
                </div>
              ) : (
                filteredArticles.map((article) => (
                  <div key={article.id} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">{article.title}</h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span className="px-2 py-1 bg-gray-100 rounded text-xs">
                            {article.category}
                          </span>
                          {article.status === 'published' ? (
                            <>
                              <span>Published {article.publishedAt}</span>
                              <span>Updated {article.updatedAt}</span>
                              <span>{article.views} views</span>
                              <span>{article.likes} likes</span>
                            </>
                          ) : (
                            <span>Last updated {article.updatedAt}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {article.status === 'draft' ? (
                          <>
                            <Link
                              href={`/create?edit=${article.id}`}
                              className="px-3 py-1 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                            >
                              Edit
                            </Link>
                            <button
                              onClick={() => handlePublish(article.id)}
                              className="px-3 py-1 text-sm text-white bg-green-600 hover:bg-green-700 rounded transition-colors"
                            >
                              Publish
                            </button>
                            <button
                              onClick={() => handleDeleteDraft(article.id)}
                              className="px-3 py-1 text-sm text-white bg-red-600 hover:bg-red-700 rounded transition-colors"
                            >
                              Delete
                            </button>
                          </>
                        ) : (
                          <>
                            <Link
                              href={`/create?edit=${article.id}`}
                              className="px-3 py-1 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                            >
                              Edit
                            </Link>
                            <button 
                              onClick={() => handleUnpublish(article.id)}
                              className="px-3 py-1 text-sm text-white bg-orange-600 hover:bg-orange-700 rounded transition-colors"
                            >
                              Unpublish
                            </button>
                            <button 
                              onClick={() => handleDelete(article.id)}
                              className="px-3 py-1 text-sm text-white bg-red-600 hover:bg-red-700 rounded transition-colors"
                            >
                              Delete
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function ContentManagementPage() {
  return (
    <AuthGuard requireAuth={true} redirectTo="/auth/login">
      <ContentManagementContent />
    </AuthGuard>
  );
}
