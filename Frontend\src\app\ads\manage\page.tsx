'use client';

import { useState, useEffect } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Upload, Calendar, Target, DollarSign, Eye, Settings } from 'lucide-react';
import Header from '@/components/Header';

interface CampaignForm {
  title: string;
  description: string;
  type: 'banner' | 'video' | 'native' | 'popup';
  budget: string;
  dailyBudget: string;
  startDate: string;
  endDate: string;
  targetAudience: {
    ageMin: string;
    ageMax: string;
    interests: string[];
    locations: string[];
  };
  adContent: {
    headline: string;
    description: string;
    imageUrl: string;
    linkUrl: string;
  };
}

export default function AdManagePage() {
  const { user, isLoading } = useSimpleAuth();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<CampaignForm>({
    title: '',
    description: '',
    type: 'banner',
    budget: '',
    dailyBudget: '',
    startDate: '',
    endDate: '',
    targetAudience: {
      ageMin: '18',
      ageMax: '65',
      interests: [],
      locations: []
    },
    adContent: {
      headline: '',
      description: '',
      imageUrl: '',
      linkUrl: ''
    }
  });

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/auth/login');
      return;
    }
  }, [user, isLoading, router]);

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof CampaignForm],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('Campaign created successfully!');
      router.push('/ads');
    } catch (error) {
      alert('Failed to create campaign. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const steps = [
    { id: 1, title: 'Campaign Details', icon: Settings },
    { id: 2, title: 'Budget & Schedule', icon: DollarSign },
    { id: 3, title: 'Target Audience', icon: Target },
    { id: 4, title: 'Ad Content', icon: Eye },
    { id: 5, title: 'Review & Launch', icon: Calendar }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-4xl mx-auto p-6">
        <div className="mb-8">
          <button
            onClick={() => router.push('/ads')}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Ads Dashboard</span>
          </button>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Create New Campaign</h1>
          <p className="text-gray-600">Set up your advertising campaign in a few simple steps</p>
        </div>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                currentStep >= step.id 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : 'border-gray-300 text-gray-400'
              }`}>
                <step.icon className="h-5 w-5" />
              </div>
              <div className="ml-3 hidden md:block">
                <p className={`text-sm font-medium ${
                  currentStep >= step.id ? 'text-blue-600' : 'text-gray-400'
                }`}>
                  {step.title}
                </p>
              </div>
              {index < steps.length - 1 && (
                <div className={`w-12 h-0.5 mx-4 ${
                  currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        {/* Step 1: Campaign Details */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-6">Campaign Details</h2>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Campaign Title</label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter campaign title"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe your campaign objectives"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Ad Type</label>
              <select
                value={formData.type}
                onChange={(e) => handleInputChange('type', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="banner">Banner Ad</option>
                <option value="video">Video Ad</option>
                <option value="native">Native Ad</option>
                <option value="popup">Popup Ad</option>
              </select>
            </div>
          </div>
        )}

        {/* Step 2: Budget & Schedule */}
        {currentStep === 2 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-6">Budget & Schedule</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Total Budget ($)</label>
                <input
                  type="number"
                  value={formData.budget}
                  onChange={(e) => handleInputChange('budget', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="1000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Daily Budget ($)</label>
                <input
                  type="number"
                  value={formData.dailyBudget}
                  onChange={(e) => handleInputChange('dailyBudget', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="50"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                <input
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                <input
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Target Audience */}
        {currentStep === 3 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-6">Target Audience</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Age Range</label>
                <div className="flex space-x-3">
                  <input
                    type="number"
                    value={formData.targetAudience.ageMin}
                    onChange={(e) => handleInputChange('targetAudience.ageMin', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="18"
                  />
                  <span className="flex items-center text-gray-500">to</span>
                  <input
                    type="number"
                    value={formData.targetAudience.ageMax}
                    onChange={(e) => handleInputChange('targetAudience.ageMax', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="65"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Interests (comma-separated)</label>
              <input
                type="text"
                onChange={(e) => handleInputChange('targetAudience.interests', e.target.value.split(',').map(s => s.trim()))}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="technology, programming, design"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Target Locations (comma-separated)</label>
              <input
                type="text"
                onChange={(e) => handleInputChange('targetAudience.locations', e.target.value.split(',').map(s => s.trim()))}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="United States, Canada, United Kingdom"
              />
            </div>
          </div>
        )}

        {/* Step 4: Ad Content */}
        {currentStep === 4 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-6">Ad Content</h2>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Headline</label>
              <input
                type="text"
                value={formData.adContent.headline}
                onChange={(e) => handleInputChange('adContent.headline', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Compelling headline for your ad"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Ad Description</label>
              <textarea
                value={formData.adContent.description}
                onChange={(e) => handleInputChange('adContent.description', e.target.value)}
                rows={3}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe your product or service"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Image/Video URL</label>
              <input
                type="url"
                value={formData.adContent.imageUrl}
                onChange={(e) => handleInputChange('adContent.imageUrl', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://example.com/image.jpg"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Landing Page URL</label>
              <input
                type="url"
                value={formData.adContent.linkUrl}
                onChange={(e) => handleInputChange('adContent.linkUrl', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://your-website.com"
              />
            </div>
          </div>
        )}

        {/* Step 5: Review & Launch */}
        {currentStep === 5 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-6">Review & Launch</h2>
            
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-semibold mb-4">Campaign Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div><strong>Title:</strong> {formData.title}</div>
                <div><strong>Type:</strong> {formData.type}</div>
                <div><strong>Budget:</strong> ${formData.budget}</div>
                <div><strong>Daily Budget:</strong> ${formData.dailyBudget}</div>
                <div><strong>Duration:</strong> {formData.startDate} to {formData.endDate}</div>
                <div><strong>Age Range:</strong> {formData.targetAudience.ageMin}-{formData.targetAudience.ageMax}</div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800 text-sm">
                <strong>Note:</strong> Your campaign will be reviewed within 24 hours before going live. 
                You'll receive an email notification once it's approved.
              </p>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
          <button
            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>

          {currentStep < 5 ? (
            <button
              onClick={() => setCurrentStep(Math.min(5, currentStep + 1))}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Next
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {isSubmitting ? 'Creating...' : 'Launch Campaign'}
            </button>
          )}
        </div>
      </div>
      </div>
    </div>
  );
}