'use client';

import React, { useRef, useState, useCallback, useEffect } from 'react';
import { 
  Bold, 
  Italic, 
  Underline, 
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,
  Quote,
  Link,
  Image,
  Video,
  Music,
  Code,
  Type,
  Palette,
  Undo,
  Redo,
  Eye,
  EyeOff,
  Heading1,
  Heading2,
  Heading3,
  Table,
  Smile
} from 'lucide-react';

interface EnhancedRichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  maxHeight?: string;
  showPreview?: boolean;
  enableAdvancedFeatures?: boolean;
}

/**
 * 增强的富文本编辑器组件
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const EnhancedRichTextEditor: React.FC<EnhancedRichTextEditorProps> = ({ 
  value, 
  onChange, 
  placeholder = 'Start writing your amazing content...',
  className = '',
  maxHeight = '500px',
  showPreview = true,
  enableAdvancedFeatures = true
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);
  const audioInputRef = useRef<HTMLInputElement>(null);
  const linkInputRef = useRef<HTMLInputElement>(null);
  const isUpdatingRef = useRef(false);
  
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [selectedColor, setSelectedColor] = useState('#000000');
  const [selectedBgColor, setSelectedBgColor] = useState('#ffffff');
  const [fontSize, setFontSize] = useState('16');
  const [fontFamily, setFontFamily] = useState('Inter');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  // 工具栏状态
  const [toolbarState, setToolbarState] = useState({
    bold: false,
    italic: false,
    underline: false,
    strikethrough: false,
    alignLeft: false,
    alignCenter: false,
    alignRight: false,
    alignJustify: false
  });

  // 常用颜色
  const commonColors = [
    '#000000', '#333333', '#666666', '#999999', '#CCCCCC', '#FFFFFF',
    '#FF0000', '#FF6600', '#FFCC00', '#00FF00', '#0066FF', '#6600FF',
    '#FF3366', '#FF9933', '#FFFF33', '#33FF33', '#3366FF', '#9933FF'
  ];

  // 常用表情符号
  const commonEmojis = [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
    '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
    '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
    '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏'
  ];

  // 同步外部value变化，避免闪烁
  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      isUpdatingRef.current = true;
      editorRef.current.innerHTML = value;
      isUpdatingRef.current = false;
    }
  }, [value]);

  const handleChange = useCallback(() => {
    if (editorRef.current && !isUpdatingRef.current) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  }, [onChange]);

  const executeCommand = useCallback((command: string, value: string = '') => {
    try {
      document.execCommand(command, false, value);
      editorRef.current?.focus();
      handleChange();
      updateToolbarState();
    } catch (error) {
      console.error('Execute command error:', error);
    }
  }, [handleChange]);

  // 更新工具栏状态
  const updateToolbarState = useCallback(() => {
    try {
      setToolbarState({
        bold: document.queryCommandState('bold'),
        italic: document.queryCommandState('italic'),
        underline: document.queryCommandState('underline'),
        strikethrough: document.queryCommandState('strikeThrough'),
        alignLeft: document.queryCommandState('justifyLeft'),
        alignCenter: document.queryCommandState('justifyCenter'),
        alignRight: document.queryCommandState('justifyRight'),
        alignJustify: document.queryCommandState('justifyFull')
      });
    } catch (error) {
      console.error('Update toolbar state error:', error);
    }
  }, []);

  // 监听选择变化
  useEffect(() => {
    const handleSelectionChange = () => {
      updateToolbarState();
    };

    document.addEventListener('selectionchange', handleSelectionChange);
    return () => document.removeEventListener('selectionchange', handleSelectionChange);
  }, [updateToolbarState]);

  // 插入媒体文件
  const insertMedia = useCallback((type: 'image' | 'video' | 'audio') => {
    const inputRef = type === 'image' ? imageInputRef : type === 'video' ? videoInputRef : audioInputRef;
    inputRef.current?.click();
  }, []);

  // 处理媒体上传
  const handleMediaUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>, type: 'image' | 'video' | 'audio') => {
    const file = e.target.files?.[0];
    if (file && editorRef.current) {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result && editorRef.current) {
          const src = event.target.result as string;
          let element = '';
          
          if (type === 'image') {
            element = `<img src="${src}" alt="Uploaded image" style="max-width: 100%; height: auto; border-radius: 8px; margin: 8px 0;" />`;
          } else if (type === 'video') {
            element = `<video controls style="max-width: 100%; height: auto; border-radius: 8px; margin: 8px 0;"><source src="${src}" type="${file.type}">Your browser does not support the video tag.</video>`;
          } else if (type === 'audio') {
            element = `<audio controls style="width: 100%; margin: 8px 0;"><source src="${src}" type="${file.type}">Your browser does not support the audio tag.</audio>`;
          }
          
          document.execCommand('insertHTML', false, element);
          handleChange();
        }
      };
      reader.readAsDataURL(file);
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = '';
  }, [handleChange]);

  // 插入链接
  const insertLink = useCallback(() => {
    const url = prompt('Enter URL:');
    if (url) {
      const selection = window.getSelection();
      const selectedText = selection?.toString() || url;
      executeCommand('insertHTML', `<a href="${url}" target="_blank" style="color: #3b82f6; text-decoration: underline;">${selectedText}</a>`);
    }
  }, [executeCommand]);

  // 插入表格
  const insertTable = useCallback(() => {
    const rows = prompt('Number of rows:', '3');
    const cols = prompt('Number of columns:', '3');
    
    if (rows && cols) {
      const numRows = parseInt(rows);
      const numCols = parseInt(cols);
      
      let tableHTML = '<table style="border-collapse: collapse; width: 100%; margin: 16px 0; border: 1px solid #e5e7eb;">';
      
      for (let i = 0; i < numRows; i++) {
        tableHTML += '<tr>';
        for (let j = 0; j < numCols; j++) {
          tableHTML += `<td style="border: 1px solid #e5e7eb; padding: 8px; ${i === 0 ? 'background-color: #f9fafb; font-weight: 600;' : ''}">${i === 0 ? `Header ${j + 1}` : `Cell ${i}-${j + 1}`}</td>`;
        }
        tableHTML += '</tr>';
      }
      
      tableHTML += '</table>';
      executeCommand('insertHTML', tableHTML);
    }
  }, [executeCommand]);

  // 插入表情符号
  const insertEmoji = useCallback((emoji: string) => {
    executeCommand('insertHTML', emoji);
    setShowEmojiPicker(false);
  }, [executeCommand]);

  // 工具栏按钮组件
  const ToolbarButton: React.FC<{
    onClick: () => void;
    active?: boolean;
    title: string;
    children: React.ReactNode;
    disabled?: boolean;
  }> = ({ onClick, active = false, title, children, disabled = false }) => (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={`p-2 rounded-lg transition-all duration-200 ${
        active 
          ? 'bg-blue-100 text-blue-700 shadow-sm' 
          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-sm'}`}
    >
      {children}
    </button>
  );

  return (
    <div className={`bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden ${className}`}>
      {/* 工具栏 */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200 p-3">
        <div className="flex flex-wrap items-center gap-1">
          {/* 基础格式化 */}
          <div className="flex items-center gap-1 mr-3">
            <ToolbarButton
              onClick={() => executeCommand('bold')}
              active={toolbarState.bold}
              title="Bold (Ctrl+B)"
            >
              <Bold size={16} />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => executeCommand('italic')}
              active={toolbarState.italic}
              title="Italic (Ctrl+I)"
            >
              <Italic size={16} />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => executeCommand('underline')}
              active={toolbarState.underline}
              title="Underline (Ctrl+U)"
            >
              <Underline size={16} />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => executeCommand('strikeThrough')}
              active={toolbarState.strikethrough}
              title="Strikethrough"
            >
              <Strikethrough size={16} />
            </ToolbarButton>
          </div>

          {/* 分隔线 */}
          <div className="w-px h-6 bg-gray-300 mx-2" />

          {/* 标题 */}
          <div className="flex items-center gap-1 mr-3">
            <ToolbarButton
              onClick={() => executeCommand('formatBlock', 'h1')}
              title="Heading 1"
            >
              <Heading1 size={16} />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => executeCommand('formatBlock', 'h2')}
              title="Heading 2"
            >
              <Heading2 size={16} />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => executeCommand('formatBlock', 'h3')}
              title="Heading 3"
            >
              <Heading3 size={16} />
            </ToolbarButton>
          </div>

          {/* 分隔线 */}
          <div className="w-px h-6 bg-gray-300 mx-2" />

          {/* 对齐 */}
          <div className="flex items-center gap-1 mr-3">
            <ToolbarButton
              onClick={() => executeCommand('justifyLeft')}
              active={toolbarState.alignLeft}
              title="Align Left"
            >
              <AlignLeft size={16} />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => executeCommand('justifyCenter')}
              active={toolbarState.alignCenter}
              title="Align Center"
            >
              <AlignCenter size={16} />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => executeCommand('justifyRight')}
              active={toolbarState.alignRight}
              title="Align Right"
            >
              <AlignRight size={16} />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => executeCommand('justifyFull')}
              active={toolbarState.alignJustify}
              title="Justify"
            >
              <AlignJustify size={16} />
            </ToolbarButton>
          </div>

          {/* 分隔线 */}
          <div className="w-px h-6 bg-gray-300 mx-2" />

          {/* 列表 */}
          <div className="flex items-center gap-1 mr-3">
            <ToolbarButton
              onClick={() => executeCommand('insertUnorderedList')}
              title="Bullet List"
            >
              <List size={16} />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => executeCommand('insertOrderedList')}
              title="Numbered List"
            >
              <ListOrdered size={16} />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => executeCommand('formatBlock', 'blockquote')}
              title="Quote"
            >
              <Quote size={16} />
            </ToolbarButton>
          </div>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={imageInputRef}
        type="file"
        accept="image/*"
        onChange={(e) => handleMediaUpload(e, 'image')}
        className="hidden"
      />
      <input
        ref={videoInputRef}
        type="file"
        accept="video/*"
        onChange={(e) => handleMediaUpload(e, 'video')}
        className="hidden"
      />
      <input
        ref={audioInputRef}
        type="file"
        accept="audio/*"
        onChange={(e) => handleMediaUpload(e, 'audio')}
        className="hidden"
      />

      {/* 编辑器区域 */}
      <div className="relative">
        {isPreviewMode ? (
          <div 
            className="p-6 prose prose-lg max-w-none"
            style={{ maxHeight, overflowY: 'auto' }}
            dangerouslySetInnerHTML={{ __html: value }}
          />
        ) : (
          <div
            ref={editorRef}
            contentEditable
            onInput={handleChange}
            onFocus={updateToolbarState}
            onKeyUp={updateToolbarState}
            onMouseUp={updateToolbarState}
            className="p-6 min-h-96 focus:outline-none text-gray-900 leading-relaxed prose prose-lg max-w-none"
            style={{
              maxHeight,
              overflowY: 'auto',
              fontSize: `${fontSize}px`,
              fontFamily: fontFamily
            }}
            suppressContentEditableWarning={true}
          />
        )}
        
        {!value && !isPreviewMode && (
          <div className="absolute top-6 left-6 text-gray-400 pointer-events-none select-none">
            <span className="text-lg">{placeholder}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedRichTextEditor;
