'use client';

import React, { useMemo, useState, useEffect } from 'react';
import { TrendingUp, Users, Calendar } from 'lucide-react';

interface UserGrowthData {
  date: string;
  users: number;
  newUsers: number;
  activeUsers: number;
}

interface UserGrowthChartProps {
  data?: UserGrowthData[];
  className?: string;
  dateRange?: '7d' | '30d' | '90d';
}

/**
 * 用户增长趋势图表组件
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const UserGrowthChart: React.FC<UserGrowthChartProps> = ({ data, className = '', dateRange = '30d' }) => {
  const [chartData, setChartData] = useState<UserGrowthData[]>([]);

  // 生成模拟数据
  const generateMockData = (days: number): UserGrowthData[] => {
    const data: UserGrowthData[] = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      // 模拟用户增长数据
      const baseUsers = 1000 + Math.floor(Math.random() * 500);
      const growth = Math.floor(Math.random() * 50) + 10;
      const newUsers = Math.floor(Math.random() * 100) + 20;

      data.push({
        date: date.toISOString().split('T')[0],
        users: baseUsers + (days - i) * growth,
        newUsers,
        activeUsers: Math.floor((baseUsers + (days - i) * growth) * 0.7)
      });
    }

    return data;
  };

  // 当日期范围改变时更新数据
  useEffect(() => {
    const days = dateRange === '7d' ? 7 : dateRange === '30d' ? 30 : 90;
    const mockData = data || generateMockData(days);
    setChartData(mockData.slice(-days));
  }, [dateRange, data]);

  // 计算处理后的图表数据
  const processedChartData = useMemo(() => {
    if (!chartData || chartData.length === 0) return null;

    const maxUsers = Math.max(...chartData.map(d => d.users));
    const minUsers = Math.min(...chartData.map(d => d.users));
    const range = maxUsers - minUsers || 1;

    return chartData.map((item, index) => ({
      ...item,
      percentage: ((item.users - minUsers) / range) * 100,
      x: (index / (chartData.length - 1)) * 100,
      growth: index > 0 ? ((item.users - chartData[index - 1].users) / chartData[index - 1].users) * 100 : 0
    }));
  }, [chartData]);

  // 生成柱状图数据
  const generateBarData = useMemo(() => {
    if (!processedChartData || processedChartData.length === 0) return [];

    return processedChartData.map((item, index) => ({
      ...item,
      barHeight: item.percentage,
      x: (index / (processedChartData.length - 1)) * 100,
      width: 80 / processedChartData.length
    }));
  }, [processedChartData]);

  if (!chartData || chartData.length === 0) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
          用户增长趋势
        </h4>
        <div className="h-48 bg-gray-50 rounded-lg flex items-center justify-center">
          <div className="text-center text-gray-500">
            <Users className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>暂无数据</p>
          </div>
        </div>
      </div>
    );
  }

  const latestData = chartData[chartData.length - 1];
  const previousData = chartData[chartData.length - 2];
  const growthRate = previousData ? ((latestData.users - previousData.users) / previousData.users) * 100 : 0;
  const barData = generateBarData;

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
        用户增长趋势
      </h4>

      {/* 统计信息 */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{latestData.users.toLocaleString()}</div>
          <div className="text-sm text-gray-600">总用户数</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{latestData.newUsers.toLocaleString()}</div>
          <div className="text-sm text-gray-600">新增用户</div>
        </div>
        <div className="text-center">
          <div className={`text-2xl font-bold ${growthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {growthRate >= 0 ? '+' : ''}{growthRate.toFixed(1)}%
          </div>
          <div className="text-sm text-gray-600">增长率</div>
        </div>
      </div>

      {/* 柱状图区域 */}
      <div className="relative h-48 bg-gradient-to-b from-gray-50 to-white rounded-lg p-4">
        <div className="flex items-end justify-between h-full space-x-1">
          {barData.map((bar, index) => (
            <div
              key={index}
              className="flex-1 flex flex-col items-center group"
            >
              {/* 柱子 */}
              <div
                className="w-full bg-gradient-to-t from-blue-600 to-blue-400 rounded-t-md transition-all duration-300 hover:from-blue-700 hover:to-blue-500 relative"
                style={{ height: `${bar.barHeight}%` }}
              >
                {/* 悬浮显示数值 */}
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  {bar.users.toLocaleString()}
                </div>
              </div>

              {/* 日期标签 */}
              <div className="text-xs text-gray-500 mt-2 transform -rotate-45 origin-left">
                {new Date(bar.date).toLocaleDateString('zh-CN', {
                  month: 'short',
                  day: 'numeric'
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UserGrowthChart;
