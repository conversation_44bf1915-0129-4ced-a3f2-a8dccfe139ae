'use client';

import { useState, useEffect } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useData } from '@/contexts/DataContext';
import { useRouter } from 'next/navigation';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  User,
  MapPin,
  FileText,
  Phone,
  Mail,
  Video,
  CreditCard,
  Clock,
  DollarSign,
  Wallet,
  TrendingUp,
  Eye,
  EyeOff,
  ArrowRight,
  Info,
  Star,
  Zap,
  Lock,
  Globe,
  Smartphone,
  MessageCircle,
  Camera,
  Fingerprint,
  Scan,
  QrCode,
  Wifi,
  Database,
  Brain,
  Radar,
  Layers,
  Network,
  Cpu,
  HardDrive
} from 'lucide-react';
import AuthGuard from '@/components/AuthGuard';
import Header from '@/components/Header';

interface WithdrawalPreview {
  amount: number;
  taxRate: number;
  serviceFeeRate: number;
  methodFeeRate: number;
  taxAmount: number;
  serviceFeeAmount: number;
  methodFeeAmount: number;
  totalFeeAmount: number;
  netAmount: number;
  scheduledPaymentDate: string;
  estimatedArrival: string;
}



interface VerificationStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  required: boolean;
  estimatedTime: string;
  requirements: string[];
}

function WithdrawContent() {
  const { user, isLoading } = useSimpleAuth();
  const { balance, updateBalance, addWithdrawalRequest } = useData();
  const router = useRouter();
  const [amount, setAmount] = useState('');
  const [country, setCountry] = useState('US');
  const [withdrawalMethod, setWithdrawalMethod] = useState('bank_card');
  const [preview, setPreview] = useState<WithdrawalPreview | null>(null);
  const [showSecurity, setShowSecurity] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeVerificationStep, setActiveVerificationStep] = useState<string | null>(null);
  const [showBalance, setShowBalance] = useState(true);

  // 账户信息状态
  const [accountInfo, setAccountInfo] = useState({
    // 银行卡信息
    bankName: '',
    accountNumber: '',
    routingNumber: '',
    accountHolderName: '',
    swiftCode: '',

    // PayPal信息
    paypalEmail: '',

    // 加密货币信息
    cryptoAddress: '',
    cryptoNetwork: 'BTC',

    // Wise信息
    wiseEmail: '',

    // 支付宝信息
    alipayAccount: '',

    // 微信信息
    wechatAccount: ''
  });
  const [currentStep, setCurrentStep] = useState(1); // 1: Amount, 2: Method, 3: Verification, 4: Confirm

  // 多维度安全认证系统 - 防盗设计
  const [securityLayers, setSecurityLayers] = useState({
    // 第一层：身份认证层
    identity: {
      biometric: {
        id: 'biometric',
        title: 'Biometric Authentication',
        description: 'Facial recognition and fingerprint verification',
        icon: Camera,
        status: 'completed',
        required: true,
        securityLevel: 'critical',
        estimatedTime: '2-5 minutes',
        antiTheftFeatures: ['Liveness detection', 'Anti-spoofing', '3D facial mapping', 'Multiple angle verification'],
        requirements: ['Live facial scan', 'Fingerprint scan', 'Voice pattern recognition', 'Eye movement tracking']
      },
      document: {
        id: 'document',
        title: 'AI + Human Document Verification',
        description: 'Advanced document authenticity verification with AI and human review',
        icon: FileText,
        status: 'completed',
        required: true,
        securityLevel: 'critical',
        estimatedTime: '1-4 hours',
        antiTheftFeatures: ['OCR fraud detection', 'Watermark verification', 'Document aging analysis', 'Cross-reference validation'],
        requirements: ['Government-issued ID', 'Utility bill (≤3 months)', 'Bank statement', 'Secondary ID document']
      },
      video: {
        id: 'video',
        title: 'Live Video Verification',
        description: 'Real-time video call with security specialist',
        icon: Video,
        status: 'in_progress',
        required: true,
        securityLevel: 'critical',
        estimatedTime: '15-30 minutes',
        antiTheftFeatures: ['Real-time interaction', 'Document holding verification', 'Background analysis', 'Behavioral assessment'],
        requirements: ['Live video call', 'ID document display', 'Security questions', 'Gesture verification']
      }
    },

    // 第二层：设备与位置安全层
    device: {
      fingerprinting: {
        id: 'device_fingerprint',
        title: 'Device Fingerprinting',
        description: 'Unique device identification and trust scoring',
        icon: Smartphone,
        status: 'completed',
        required: true,
        securityLevel: 'high',
        estimatedTime: 'Instant',
        antiTheftFeatures: ['Hardware fingerprinting', 'Browser fingerprinting', 'Network analysis', 'Device reputation scoring'],
        requirements: ['Trusted device registration', 'Device history analysis', 'Hardware verification', 'Browser consistency check']
      },
      location: {
        id: 'geo_location',
        title: 'Geolocation Verification',
        description: 'Location-based security and anomaly detection',
        icon: Globe,
        status: 'completed',
        required: true,
        securityLevel: 'high',
        estimatedTime: 'Instant',
        antiTheftFeatures: ['IP geolocation', 'GPS verification', 'VPN/Proxy detection', 'Travel pattern analysis'],
        requirements: ['Consistent location history', 'IP address verification', 'GPS coordinates', 'Time zone consistency']
      }
    },

    // 第三层：行为分析层
    behavioral: {
      patterns: {
        id: 'behavior_analysis',
        title: 'Behavioral Pattern Analysis',
        description: 'AI-powered user behavior analysis and anomaly detection',
        icon: TrendingUp,
        status: 'completed',
        required: true,
        securityLevel: 'high',
        estimatedTime: 'Real-time',
        antiTheftFeatures: ['Typing pattern analysis', 'Mouse movement tracking', 'Session behavior analysis', 'Transaction pattern recognition'],
        requirements: ['Consistent typing patterns', 'Normal session behavior', 'Expected transaction amounts', 'Regular usage patterns']
      },
      risk: {
        id: 'risk_assessment',
        title: 'AI Risk Assessment',
        description: 'Machine learning-based fraud risk evaluation',
        icon: Shield,
        status: 'completed',
        required: true,
        securityLevel: 'critical',
        estimatedTime: 'Real-time',
        antiTheftFeatures: ['ML fraud detection', 'Risk scoring algorithm', 'Anomaly detection', 'Pattern recognition'],
        requirements: ['Low risk score', 'Normal account activity', 'Consistent behavior patterns', 'No fraud indicators']
      }
    },

    // 第四层：多因子认证层
    mfa: {
      sms: {
        id: 'sms_verification',
        title: 'SMS Authentication',
        description: 'Mobile phone number verification with OTP',
        icon: Phone,
        status: 'pending',
        required: true,
        securityLevel: 'medium',
        estimatedTime: '1-2 minutes',
        antiTheftFeatures: ['SIM swap detection', 'Phone number validation', 'Carrier verification', 'SMS delivery confirmation'],
        requirements: ['Registered phone number', 'SMS reception capability', 'SIM card verification', 'Carrier authentication']
      },
      email: {
        id: 'email_verification',
        title: 'Email Authentication',
        description: 'Email-based verification with secure tokens',
        icon: Mail,
        status: 'completed',
        required: true,
        securityLevel: 'medium',
        estimatedTime: '1-2 minutes',
        antiTheftFeatures: ['Email domain verification', 'DKIM/SPF validation', 'Account age verification', 'Email pattern analysis'],
        requirements: ['Verified email address', 'Email access capability', 'Domain authentication', 'Account history verification']
      },
      authenticator: {
        id: 'totp_verification',
        title: 'Authenticator App',
        description: 'Time-based one-time password (TOTP) verification',
        icon: Lock,
        status: 'pending',
        required: false,
        securityLevel: 'high',
        estimatedTime: '30 seconds',
        antiTheftFeatures: ['Time-based tokens', 'Device-specific generation', 'Offline capability', 'Backup codes'],
        requirements: ['Authenticator app setup', 'Device access', 'Time synchronization', 'Backup code storage']
      },
      hardware: {
        id: 'hardware_key',
        title: 'Hardware Security Key',
        description: 'FIDO2/WebAuthn hardware token verification',
        icon: Fingerprint,
        status: 'pending',
        required: false,
        securityLevel: 'critical',
        estimatedTime: '10 seconds',
        antiTheftFeatures: ['Physical token required', 'Cryptographic signatures', 'Tamper-resistant', 'Biometric unlock'],
        requirements: ['FIDO2 compatible key', 'USB/NFC connection', 'PIN or biometric setup', 'Browser support']
      },
      push: {
        id: 'push_notification',
        title: 'Push Notification Auth',
        description: 'Secure push notification to registered device',
        icon: Smartphone,
        status: 'pending',
        required: false,
        securityLevel: 'high',
        estimatedTime: '15 seconds',
        antiTheftFeatures: ['Device-specific tokens', 'Encrypted channels', 'Biometric confirmation', 'Location verification'],
        requirements: ['Mobile app installed', 'Push notifications enabled', 'Device registration', 'Biometric unlock']
      }
    },

    // 第五层：交易安全层
    transaction: {
      limits: {
        id: 'transaction_limits',
        title: 'Dynamic Transaction Limits',
        description: 'AI-adjusted withdrawal limits based on risk assessment',
        icon: DollarSign,
        status: 'completed',
        required: true,
        securityLevel: 'high',
        estimatedTime: 'Instant',
        antiTheftFeatures: ['Dynamic limit adjustment', 'Risk-based limits', 'Velocity checking', 'Amount pattern analysis'],
        requirements: ['Within approved limits', 'Normal transaction velocity', 'Expected amount ranges', 'Account balance verification']
      },
      cooling: {
        id: 'cooling_period',
        title: 'Security Cooling Period',
        description: 'Mandatory waiting period for high-risk transactions',
        icon: Clock,
        status: 'completed',
        required: true,
        securityLevel: 'critical',
        estimatedTime: '24-72 hours',
        antiTheftFeatures: ['Time-delayed execution', 'Cancellation window', 'Additional verification during wait', 'Risk reassessment'],
        requirements: ['Cooling period compliance', 'No cancellation requests', 'Continued verification', 'Risk level maintenance']
      }
    },

    // 第六层：AI和区块链认证层 (2024年最新技术)
    advanced: {
      ai_behavioral: {
        id: 'ai_behavioral',
        title: 'AI Behavioral Analysis',
        description: 'Machine learning-based user behavior pattern recognition',
        icon: Brain,
        status: 'completed',
        required: true,
        securityLevel: 'critical',
        estimatedTime: 'Real-time',
        antiTheftFeatures: ['Typing pattern analysis', 'Mouse movement tracking', 'Session behavior modeling', 'Anomaly detection'],
        requirements: ['Sufficient behavioral data', 'Normal usage patterns', 'Consistent interaction style', 'Device familiarity']
      },
      blockchain_identity: {
        id: 'blockchain_identity',
        title: 'Blockchain Identity Verification',
        description: 'Decentralized identity verification using blockchain',
        icon: Database,
        status: 'pending',
        required: false,
        securityLevel: 'high',
        estimatedTime: '2-5 minutes',
        antiTheftFeatures: ['Immutable identity records', 'Cryptographic proofs', 'Decentralized validation', 'Zero-knowledge proofs'],
        requirements: ['Blockchain wallet', 'Identity NFT', 'Cryptographic signatures', 'Network consensus']
      },
      zero_trust: {
        id: 'zero_trust',
        title: 'Zero Trust Network Access',
        description: 'Continuous verification with zero trust principles',
        icon: Network,
        status: 'completed',
        required: true,
        securityLevel: 'critical',
        estimatedTime: 'Continuous',
        antiTheftFeatures: ['Never trust, always verify', 'Micro-segmentation', 'Least privilege access', 'Continuous monitoring'],
        requirements: ['Network analysis', 'Traffic inspection', 'Identity verification', 'Policy compliance']
      },
      quantum_resistant: {
        id: 'quantum_resistant',
        title: 'Quantum-Resistant Cryptography',
        description: 'Post-quantum cryptographic algorithms for future security',
        icon: Cpu,
        status: 'completed',
        required: true,
        securityLevel: 'critical',
        estimatedTime: 'Instant',
        antiTheftFeatures: ['Quantum-safe algorithms', 'Lattice-based crypto', 'Hash-based signatures', 'Future-proof security'],
        requirements: ['Quantum-resistant keys', 'Algorithm support', 'Cryptographic agility', 'Security standards compliance']
      }
    }
  });

  const paymentMethods = {
    bank_card: {
      name: 'Bank Transfer',
      fee: 2.5,
      time: '1-3 business days',
      icon: CreditCard,
      description: 'Direct transfer to your bank account',
      popular: true,
      security: 'High'
    },
    paypal: {
      name: 'PayPal',
      fee: 3.5,
      time: 'Instant',
      icon: DollarSign,
      description: 'Fast and secure PayPal transfer',
      popular: false,
      security: 'High'
    },
    crypto: {
      name: 'Cryptocurrency',
      fee: 1.5,
      time: '30min-2hrs',
      icon: Shield,
      description: 'Cryptocurrency transfer to your wallet',
      popular: true,
      security: 'Very High'
    },
    wise: {
      name: 'Wise (TransferWise)',
      fee: 2.0,
      time: '1-2 business days',
      icon: Globe,
      description: 'International money transfer with low fees',
      popular: true,
      security: 'High'
    },
    alipay: {
      name: 'Alipay',
      fee: 1.8,
      time: 'Instant',
      icon: Smartphone,
      description: 'Popular Chinese digital payment platform',
      popular: true,
      security: 'High'
    },
    wechat: {
      name: 'WeChat Pay',
      fee: 1.8,
      time: 'Instant',
      icon: MessageCircle,
      description: 'WeChat integrated payment system',
      popular: true,
      security: 'High'
    }
  };

  const countries = {
    US: { name: 'United States', tax: 24, serviceFee: 2.5, flag: '🇺🇸' },
    CN: { name: 'China', tax: 20, serviceFee: 1.8, flag: '🇨🇳' },
    GB: { name: 'United Kingdom', tax: 20, serviceFee: 2.2, flag: '🇬🇧' },
    DE: { name: 'Germany', tax: 26.375, serviceFee: 2.8, flag: '🇩🇪' },
    JP: { name: 'Japan', tax: 20.315, serviceFee: 2.1, flag: '🇯🇵' },
    CA: { name: 'Canada', tax: 26.76, serviceFee: 2.4, flag: '🇨🇦' },
    AU: { name: 'Australia', tax: 32.5, serviceFee: 3.2, flag: '🇦🇺' },
    FR: { name: 'France', tax: 30, serviceFee: 2.9, flag: '🇫🇷' },
    IT: { name: 'Italy', tax: 26, serviceFee: 2.7, flag: '🇮🇹' },
    ES: { name: 'Spain', tax: 24, serviceFee: 2.6, flag: '🇪🇸' },
    NL: { name: 'Netherlands', tax: 25.8, serviceFee: 2.5, flag: '🇳🇱' },
    SE: { name: 'Sweden', tax: 30, serviceFee: 3.0, flag: '🇸🇪' },
    NO: { name: 'Norway', tax: 22, serviceFee: 2.8, flag: '🇳🇴' },
    DK: { name: 'Denmark', tax: 27, serviceFee: 2.9, flag: '🇩🇰' },
    FI: { name: 'Finland', tax: 30, serviceFee: 3.1, flag: '🇫🇮' },
    CH: { name: 'Switzerland', tax: 35, serviceFee: 3.5, flag: '🇨🇭' },
    AT: { name: 'Austria', tax: 27.5, serviceFee: 2.8, flag: '🇦🇹' },
    BE: { name: 'Belgium', tax: 30, serviceFee: 3.0, flag: '🇧🇪' },
    IE: { name: 'Ireland', tax: 20, serviceFee: 2.3, flag: '🇮🇪' },
    PT: { name: 'Portugal', tax: 28, serviceFee: 2.7, flag: '🇵🇹' },
    KR: { name: 'South Korea', tax: 22, serviceFee: 2.3 },
    SG: { name: 'Singapore', tax: 17, serviceFee: 2.0 },
    HK: { name: 'Hong Kong', tax: 17, serviceFee: 1.9 },
    TW: { name: 'Taiwan', tax: 20, serviceFee: 2.1 },
    MY: { name: 'Malaysia', tax: 24, serviceFee: 2.2 },
    TH: { name: 'Thailand', tax: 15, serviceFee: 1.8 },
    IN: { name: 'India', tax: 30, serviceFee: 2.5 },
    BR: { name: 'Brazil', tax: 27.5, serviceFee: 3.2 },
    MX: { name: 'Mexico', tax: 30, serviceFee: 2.8 },
    RU: { name: 'Russia', tax: 13, serviceFee: 2.5 },
    TR: { name: 'Turkey', tax: 20, serviceFee: 2.3 },
    SA: { name: 'Saudi Arabia', tax: 0, serviceFee: 2.0 },
    AE: { name: 'UAE', tax: 0, serviceFee: 1.8 },
    IL: { name: 'Israel', tax: 23, serviceFee: 2.4 },
    ZA: { name: 'South Africa', tax: 45, serviceFee: 3.5 },
    NG: { name: 'Nigeria', tax: 24, serviceFee: 2.2 },
    EG: { name: 'Egypt', tax: 22.5, serviceFee: 2.0 }
  };

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/auth/login');
      return;
    }
  }, [user, isLoading, router]);

  // 计算整体安全评分
  const getSecurityScore = () => {
    let totalSteps = 0;
    let completedSteps = 0;
    let criticalSteps = 0;
    let completedCriticalSteps = 0;

    Object.values(securityLayers).forEach(layer => {
      Object.values(layer).forEach(step => {
        totalSteps++;
        if (step.status === 'completed') completedSteps++;
        if (step.securityLevel === 'critical') {
          criticalSteps++;
          if (step.status === 'completed') completedCriticalSteps++;
        }
      });
    });

    // 关键步骤权重更高
    const criticalWeight = 0.7;
    const regularWeight = 0.3;

    const criticalScore = criticalSteps > 0 ? (completedCriticalSteps / criticalSteps) * criticalWeight : 0;
    const regularScore = totalSteps > 0 ? (completedSteps / totalSteps) * regularWeight : 0;

    return Math.round((criticalScore + regularScore) * 100);
  };

  // 检查是否满足提现安全要求
  const isSecurityCompliant = () => {
    const criticalSteps = [];
    Object.values(securityLayers).forEach(layer => {
      Object.values(layer).forEach(step => {
        if (step.required && step.securityLevel === 'critical') {
          criticalSteps.push(step);
        }
      });
    });

    return criticalSteps.every(step => step.status === 'completed') && getSecurityScore() >= 85;
  };

  // 获取安全风险等级
  const getSecurityRiskLevel = () => {
    const score = getSecurityScore();
    if (score >= 90) return { level: 'Low', color: 'green', description: 'Excellent security posture' };
    if (score >= 75) return { level: 'Medium', color: 'yellow', description: 'Good security, minor improvements needed' };
    if (score >= 50) return { level: 'High', color: 'orange', description: 'Security concerns detected' };
    return { level: 'Critical', color: 'red', description: 'Immediate security action required' };
  };

  // 处理安全验证步骤
  const handleSecurityStepAction = async (layerId: string, stepId: string) => {
    setActiveVerificationStep(stepId);

    // 模拟不同类型的验证过程
    const step = securityLayers[layerId as keyof typeof securityLayers][stepId];

    if (step.id === 'biometric') {
      // 模拟生物识别验证
      setTimeout(() => {
        updateSecurityStep(layerId, stepId, 'in_progress');
        setTimeout(() => {
          updateSecurityStep(layerId, stepId, 'completed');
          setActiveVerificationStep(null);
          alert('✅ Biometric verification completed! Facial recognition and fingerprint verified.');
        }, 5000);
      }, 1000);
    } else if (step.id === 'video') {
      // 模拟视频通话安排
      setTimeout(() => {
        updateSecurityStep(layerId, stepId, 'in_progress');
        setTimeout(() => {
          updateSecurityStep(layerId, stepId, 'completed');
          setActiveVerificationStep(null);
          alert('✅ Video verification completed! Identity confirmed by security specialist.');
        }, 8000);
      }, 1000);
    } else {
      // 标准验证流程
      setTimeout(() => {
        updateSecurityStep(layerId, stepId, 'in_progress');
        setTimeout(() => {
          updateSecurityStep(layerId, stepId, 'completed');
          setActiveVerificationStep(null);
          alert(`✅ ${step.title} completed successfully!`);
        }, 3000);
      }, 1000);
    }
  };

  // 更新安全步骤状态
  const updateSecurityStep = (layerId: string, stepId: string, status: string) => {
    setSecurityLayers(prev => ({
      ...prev,
      [layerId]: {
        ...prev[layerId as keyof typeof prev],
        [stepId]: {
          ...prev[layerId as keyof typeof prev][stepId],
          status
        }
      }
    }));
  };

  // 兼容性函数（保持原有接口）
  const getOverallProgress = () => getSecurityScore();
  const isFullyVerified = () => isSecurityCompliant();
  const handleStepAction = (stepId: string) => {
    // 查找步骤所在的层级
    for (const [layerId, layer] of Object.entries(securityLayers)) {
      if (layer[stepId]) {
        handleSecurityStepAction(layerId, stepId);
        break;
      }
    }
  };

  // 验证账户信息完整性
  const isAccountInfoComplete = () => {
    switch (withdrawalMethod) {
      case 'bank_card':
        return accountInfo.bankName && accountInfo.accountNumber && accountInfo.routingNumber && accountInfo.accountHolderName;
      case 'paypal':
        return accountInfo.paypalEmail;
      case 'crypto':
        return accountInfo.cryptoAddress && accountInfo.cryptoNetwork;
      case 'wise':
        return accountInfo.wiseEmail;
      case 'alipay':
        return accountInfo.alipayAccount;
      case 'wechat':
        return accountInfo.wechatAccount;
      default:
        return false;
    }
  };

  const handlePreview = () => {
    if (!isFullyVerified()) {
      alert('Please complete all verification steps before making a withdrawal.');
      return;
    }

    if (!amount || parseFloat(amount) < 100) {
      alert('Minimum withdrawal amount is $100');
      return;
    }

    if (parseFloat(amount) > balance.availableBalance) {
      alert(`Withdrawal amount cannot exceed available balance ($${balance.availableBalance.toFixed(2)})`);
      return;
    }

    if (!isAccountInfoComplete()) {
      alert('Please complete all required account information fields.');
      return;
    }

    const amountNum = parseFloat(amount);
    const method = paymentMethods[withdrawalMethod as keyof typeof paymentMethods];
    const countryInfo = countries[country as keyof typeof countries];
    
    const taxRate = countryInfo.tax / 100;
    const serviceFeeRate = countryInfo.serviceFee / 100;
    const methodFeeRate = method.fee / 100;
    
    const taxAmount = parseFloat((amountNum * taxRate).toFixed(2));
    const serviceFeeAmount = parseFloat((amountNum * serviceFeeRate).toFixed(2));
    const methodFeeAmount = parseFloat((amountNum * methodFeeRate).toFixed(2));
    const totalFeeAmount = serviceFeeAmount + methodFeeAmount;
    const netAmount = parseFloat((amountNum - taxAmount - totalFeeAmount).toFixed(2));

    const scheduledDate = new Date();
    scheduledDate.setDate(scheduledDate.getDate() + 7);
    
    setPreview({
      amount: amountNum,
      taxRate,
      serviceFeeRate,
      methodFeeRate,
      taxAmount,
      serviceFeeAmount,
      methodFeeAmount,
      totalFeeAmount,
      netAmount,
      scheduledPaymentDate: scheduledDate.toISOString(),
      estimatedArrival: method.time
    });
  };

  const handleSecurityVerification = () => {
    if (!preview) return;
    setShowSecurity(true);
  };

  const handleFinalSubmit = async () => {
    if (verificationCode !== '123456') {
      alert('Invalid verification code. For demo, please enter: 123456');
      return;
    }

    setIsSubmitting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (preview) {
        // 实时更新余额数据
        updateBalance({
          availableBalance: balance.availableBalance - preview.amount,
          pendingBalance: balance.pendingBalance + preview.netAmount
        });
        
        // 添加提现记录
        const newRequest = {
          id: Date.now(),
          amount: preview.amount,
          netAmount: preview.netAmount,
          withdrawalMethod,
          status: 'processing',
          country,
          taxAmount: preview.taxAmount,
          feeAmount: preview.totalFeeAmount,
          scheduledPaymentDate: preview.scheduledPaymentDate,
          createdAt: new Date().toISOString()
        };
        
        addWithdrawalRequest(newRequest);
        
        // 触发storage事件以通知其他页面
        window.dispatchEvent(new Event('storage'));
      }
      
      alert('Withdrawal request submitted successfully!');
      router.push('/withdraw/history');
    } catch (error) {
      alert('Withdrawal request failed. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (showSecurity) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <Header />
        <div className="max-w-lg mx-auto pt-20 p-6">
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8">
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Shield className="h-10 w-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-3">Security Verification</h2>
              <p className="text-gray-600">
                Protect your withdrawal with two-factor authentication
              </p>
            </div>

            <div className="space-y-6">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
                <div className="flex items-start">
                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                    <Mail className="h-5 w-5 text-white" />
                  </div>
                  <div className="text-sm text-blue-800">
                    <p className="font-semibold mb-2">Email Verification</p>
                    <p className="mb-3">We've sent a 6-digit verification code to your registered email address</p>
                    <div className="bg-blue-100 border border-blue-200 rounded-lg p-3">
                      <p className="text-xs text-blue-700 font-medium">
                        🔒 Demo Mode: Enter <span className="bg-blue-200 px-2 py-1 rounded font-mono">123456</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Enter Verification Code
                </label>
                <div className="relative">
                  <input
                    type="text"
                    maxLength={6}
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                    className="w-full p-6 border-2 border-gray-300 rounded-xl text-center text-2xl tracking-[0.5em] font-mono focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 transition-all"
                    placeholder="● ● ● ● ● ●"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-4">
                    {verificationCode.length === 6 && (
                      <CheckCircle className="h-6 w-6 text-green-500" />
                    )}
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2 text-center">
                  Didn't receive the code? <button className="text-blue-600 hover:underline">Resend</button>
                </p>
              </div>

              {preview && (
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-xl p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Final Confirmation</h3>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center py-2 border-b border-gray-200">
                      <span className="text-gray-600">Withdrawal Amount:</span>
                      <span className="font-semibold text-lg">${preview.amount}</span>
                    </div>
                    <div className="flex justify-between items-center py-1">
                      <span className="text-gray-600">Payment Method:</span>
                      <span className="font-medium">{paymentMethods[withdrawalMethod as keyof typeof paymentMethods].name}</span>
                    </div>

                    <div className="bg-white rounded-lg p-3 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-red-600">Tax ({(preview.taxRate * 100).toFixed(1)}%):</span>
                        <span className="text-red-600 font-medium">-${preview.taxAmount}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-orange-600">Service Fee:</span>
                        <span className="text-orange-600 font-medium">-${preview.serviceFeeAmount}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-red-600">Method Fee:</span>
                        <span className="text-red-600 font-medium">-${preview.methodFeeAmount}</span>
                      </div>
                    </div>

                    <div className="flex justify-between items-center py-2 border-t border-gray-200 font-bold text-lg">
                      <span className="text-gray-900">You'll Receive:</span>
                      <span className="text-green-600">${preview.netAmount}</span>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex space-x-4 pt-6">
                <button
                  onClick={() => setShowSecurity(false)}
                  className="flex-1 bg-gray-200 text-gray-700 py-4 rounded-xl hover:bg-gray-300 transition-all font-semibold"
                >
                  Cancel
                </button>
                <button
                  onClick={handleFinalSubmit}
                  disabled={isSubmitting || verificationCode.length !== 6}
                  className={`flex-1 py-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center ${
                    isSubmitting || verificationCode.length !== 6
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-gradient-to-r from-green-600 to-emerald-600 text-white hover:from-green-700 hover:to-emerald-700 shadow-lg hover:shadow-xl transform hover:scale-105'
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-5 w-5 mr-2" />
                      Confirm Withdrawal
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <Header />

      <div className="max-w-6xl mx-auto px-4 py-6 pt-20">
        {/* Header Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-3">
            Withdraw Funds
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Secure and fast withdrawal process with multiple payment options
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4 mb-6">
            {[
              { step: 1, title: 'Amount', icon: DollarSign },
              { step: 2, title: 'Method', icon: CreditCard },
              { step: 3, title: 'Verify', icon: Shield },
              { step: 4, title: 'Confirm', icon: CheckCircle }
            ].map((item, index) => {
              const Icon = item.icon;
              const isActive = currentStep === item.step;
              const isCompleted = currentStep > item.step;

              return (
                <div key={item.step} className="flex items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                    isCompleted
                      ? 'bg-green-500 border-green-500 text-white'
                      : isActive
                        ? 'bg-blue-500 border-blue-500 text-white'
                        : 'bg-white border-gray-300 text-gray-400'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6" />
                    ) : (
                      <Icon className="w-6 h-6" />
                    )}
                  </div>
                  <span className={`ml-2 text-sm font-medium ${
                    isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    {item.title}
                  </span>
                  {index < 3 && (
                    <div className={`w-16 h-0.5 mx-4 ${
                      currentStep > item.step ? 'bg-green-500' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Account Balance Card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center">
              <Wallet className="h-7 w-7 mr-3 text-blue-600" />
              Account Balance
            </h2>
            <button
              onClick={() => setShowBalance(!showBalance)}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
            >
              {showBalance ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="relative overflow-hidden bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl p-6 text-white">
              <div className="relative z-10">
                <p className="text-green-100 text-sm font-medium mb-2">Available Balance</p>
                <p className="text-3xl font-bold">
                  {showBalance ? `$${balance.availableBalance}` : '••••••'}
                </p>
                <div className="flex items-center mt-2 text-green-100">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  <span className="text-xs">Ready to withdraw</span>
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -mr-10 -mt-10" />
            </div>

            <div className="relative overflow-hidden bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl p-6 text-white">
              <div className="relative z-10">
                <p className="text-yellow-100 text-sm font-medium mb-2">Pending</p>
                <p className="text-2xl font-bold">
                  {showBalance ? `$${balance.pendingBalance}` : '••••••'}
                </p>
                <div className="flex items-center mt-2 text-yellow-100">
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="text-xs">Processing</span>
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -mr-10 -mt-10" />
            </div>

            <div className="relative overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-6 text-white">
              <div className="relative z-10">
                <p className="text-blue-100 text-sm font-medium mb-2">Total Earned</p>
                <p className="text-2xl font-bold">
                  {showBalance ? `$${balance.totalEarnings}` : '••••••'}
                </p>
                <div className="flex items-center mt-2 text-blue-100">
                  <Star className="h-4 w-4 mr-1" />
                  <span className="text-xs">All time</span>
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -mr-10 -mt-10" />
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Multi-Dimensional Security Status */}
          <div className="lg:col-span-1">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 sticky top-24">
              {/* Security Score Header */}
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 via-yellow-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">🛡️ Security Center</h3>
                <p className="text-sm text-gray-600 mb-3">Multi-layered anti-theft protection</p>

                {/* Security Score Display */}
                <div className="relative mb-4">
                  <div className="w-full bg-gray-200 rounded-full h-4 mb-2 overflow-hidden">
                    <div
                      className={`h-4 rounded-full transition-all duration-500 shadow-sm ${
                        getSecurityScore() >= 90 ? 'bg-gradient-to-r from-green-500 to-emerald-600' :
                        getSecurityScore() >= 75 ? 'bg-gradient-to-r from-yellow-500 to-orange-500' :
                        getSecurityScore() >= 50 ? 'bg-gradient-to-r from-orange-500 to-red-500' :
                        'bg-gradient-to-r from-red-500 to-red-700'
                      }`}
                      style={{ width: `${getSecurityScore()}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold text-gray-900">{getSecurityScore()}% Secure</span>
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                      getSecurityRiskLevel().color === 'green' ? 'bg-green-100 text-green-800' :
                      getSecurityRiskLevel().color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                      getSecurityRiskLevel().color === 'orange' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {getSecurityRiskLevel().level} Risk
                    </span>
                  </div>
                </div>
              </div>

              {/* Security Layers Display */}
              <div className="space-y-4">
                {/* Layer 1: Identity Authentication */}
                <div className="border border-gray-200 rounded-xl p-4 bg-gradient-to-r from-red-50 to-pink-50">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-bold text-gray-900 flex items-center">
                      <User className="h-4 w-4 mr-2 text-red-600" />
                      Layer 1: Identity
                    </h4>
                    <span className="text-xs px-2 py-1 bg-red-100 text-red-800 rounded-full font-medium">Critical</span>
                  </div>
                  <div className="space-y-2">
                    {Object.values(securityLayers.identity).map((step) => {
                      const Icon = step.icon;
                      return (
                        <div key={step.id} className="flex items-center justify-between text-xs">
                          <div className="flex items-center">
                            <div className={`w-4 h-4 rounded-full flex items-center justify-center mr-2 ${
                              step.status === 'completed' ? 'bg-green-500' :
                              step.status === 'in_progress' ? 'bg-blue-500' :
                              'bg-gray-400'
                            }`}>
                              {step.status === 'completed' ? (
                                <CheckCircle className="h-2 w-2 text-white" />
                              ) : step.status === 'in_progress' ? (
                                <div className="animate-spin rounded-full h-2 w-2 border border-white border-t-transparent"></div>
                              ) : (
                                <Icon className="h-2 w-2 text-white" />
                              )}
                            </div>
                            <span className="text-gray-700">{step.title}</span>
                          </div>
                          {step.status !== 'completed' && (
                            <button
                              onClick={() => handleSecurityStepAction('identity', step.id)}
                              disabled={activeVerificationStep === step.id}
                              className="text-blue-600 hover:text-blue-800 font-medium"
                            >
                              {step.status === 'in_progress' ? 'Processing...' : 'Start'}
                            </button>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Layer 2: Device & Location */}
                <div className="border border-gray-200 rounded-xl p-4 bg-gradient-to-r from-blue-50 to-indigo-50">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-bold text-gray-900 flex items-center">
                      <Smartphone className="h-4 w-4 mr-2 text-blue-600" />
                      Layer 2: Device & Location
                    </h4>
                    <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full font-medium">High</span>
                  </div>
                  <div className="space-y-2">
                    {Object.values(securityLayers.device).map((step) => {
                      const Icon = step.icon;
                      return (
                        <div key={step.id} className="flex items-center justify-between text-xs">
                          <div className="flex items-center">
                            <div className={`w-4 h-4 rounded-full flex items-center justify-center mr-2 ${
                              step.status === 'completed' ? 'bg-green-500' : 'bg-gray-400'
                            }`}>
                              {step.status === 'completed' ? (
                                <CheckCircle className="h-2 w-2 text-white" />
                              ) : (
                                <Icon className="h-2 w-2 text-white" />
                              )}
                            </div>
                            <span className="text-gray-700">{step.title}</span>
                          </div>
                          <span className="text-green-600 font-medium">✓ Active</span>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Layer 3: Behavioral Analysis */}
                <div className="border border-gray-200 rounded-xl p-4 bg-gradient-to-r from-purple-50 to-pink-50">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-bold text-gray-900 flex items-center">
                      <TrendingUp className="h-4 w-4 mr-2 text-purple-600" />
                      Layer 3: AI Analysis
                    </h4>
                    <span className="text-xs px-2 py-1 bg-purple-100 text-purple-800 rounded-full font-medium">High</span>
                  </div>
                  <div className="space-y-2">
                    {Object.values(securityLayers.behavioral).map((step) => {
                      const Icon = step.icon;
                      return (
                        <div key={step.id} className="flex items-center justify-between text-xs">
                          <div className="flex items-center">
                            <div className="w-4 h-4 rounded-full flex items-center justify-center mr-2 bg-green-500">
                              <CheckCircle className="h-2 w-2 text-white" />
                            </div>
                            <span className="text-gray-700">{step.title}</span>
                          </div>
                          <span className="text-green-600 font-medium">✓ Monitoring</span>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Layer 4: Multi-Factor Auth */}
                <div className="border border-gray-200 rounded-xl p-4 bg-gradient-to-r from-yellow-50 to-orange-50">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-bold text-gray-900 flex items-center">
                      <Lock className="h-4 w-4 mr-2 text-yellow-600" />
                      Layer 4: Multi-Factor Auth
                    </h4>
                    <span className="text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full font-medium">Medium</span>
                  </div>
                  <div className="space-y-2">
                    {Object.values(securityLayers.mfa).map((step) => {
                      const Icon = step.icon;
                      return (
                        <div key={step.id} className="flex items-center justify-between text-xs">
                          <div className="flex items-center">
                            <div className={`w-4 h-4 rounded-full flex items-center justify-center mr-2 ${
                              step.status === 'completed' ? 'bg-green-500' :
                              step.status === 'in_progress' ? 'bg-blue-500' :
                              'bg-gray-400'
                            }`}>
                              {step.status === 'completed' ? (
                                <CheckCircle className="h-2 w-2 text-white" />
                              ) : step.status === 'in_progress' ? (
                                <div className="animate-spin rounded-full h-2 w-2 border border-white border-t-transparent"></div>
                              ) : (
                                <Icon className="h-2 w-2 text-white" />
                              )}
                            </div>
                            <span className="text-gray-700">{step.title}</span>
                          </div>
                          {step.status !== 'completed' && step.required && (
                            <button
                              onClick={() => handleSecurityStepAction('mfa', step.id)}
                              disabled={activeVerificationStep === step.id}
                              className="text-blue-600 hover:text-blue-800 font-medium"
                            >
                              {step.status === 'in_progress' ? 'Processing...' : 'Verify'}
                            </button>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Advanced AI & Blockchain Security Layer */}
              <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 border border-purple-200">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-full flex items-center justify-center mr-4">
                    <Brain className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">🤖 AI & Blockchain Security</h3>
                    <p className="text-sm text-gray-600">Next-generation security technologies</p>
                  </div>
                </div>

                <div className="space-y-4">
                  {Object.values(securityLayers.advanced).map((step) => {
                    const Icon = step.icon;
                    return (
                      <div key={step.id} className="flex items-center justify-between p-4 bg-white/70 backdrop-blur-sm rounded-xl border border-purple-100">
                        <div className="flex items-center">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                            step.status === 'completed' ? 'bg-green-500' :
                            step.status === 'in_progress' ? 'bg-yellow-500' :
                            step.status === 'failed' ? 'bg-red-500' : 'bg-gray-400'
                          }`}>
                            {step.status === 'completed' ? (
                              <CheckCircle className="h-4 w-4 text-white" />
                            ) : step.status === 'in_progress' ? (
                              <Clock className="h-4 w-4 text-white animate-spin" />
                            ) : step.status === 'failed' ? (
                              <AlertTriangle className="h-4 w-4 text-white" />
                            ) : (
                              <Icon className="h-4 w-4 text-white" />
                            )}
                          </div>
                          <div>
                            <div className="flex items-center">
                              <span className="text-sm font-medium text-gray-900">{step.title}</span>
                              <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                                step.securityLevel === 'critical' ? 'bg-red-100 text-red-700' :
                                step.securityLevel === 'high' ? 'bg-orange-100 text-orange-700' :
                                'bg-blue-100 text-blue-700'
                              }`}>
                                {step.securityLevel}
                              </span>
                            </div>
                            <p className="text-xs text-gray-600">{step.description}</p>
                            <div className="flex items-center mt-1">
                              <Clock className="h-3 w-3 text-gray-400 mr-1" />
                              <span className="text-xs text-gray-500">{step.estimatedTime}</span>
                            </div>
                          </div>
                        </div>
                        {step.status !== 'completed' && step.required && (
                          <button
                            onClick={() => handleSecurityStepAction('advanced', step.id)}
                            disabled={activeVerificationStep === step.id}
                            className="text-purple-600 hover:text-purple-800 font-medium text-sm px-3 py-1 rounded-lg border border-purple-200 hover:bg-purple-50 transition-colors"
                          >
                            {step.status === 'in_progress' ? 'Processing...' : 'Activate'}
                          </button>
                        )}
                      </div>
                    );
                  })}
                </div>

                {/* AI Security Insights */}
                <div className="mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl border border-indigo-200">
                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center mr-3">
                      <Radar className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-indigo-900 mb-2">🧠 AI Security Analysis</h4>
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <span className="text-indigo-700 font-medium">Behavioral Score:</span>
                          <span className="text-green-600 ml-1">98.5% Normal</span>
                        </div>
                        <div>
                          <span className="text-indigo-700 font-medium">Risk Level:</span>
                          <span className="text-green-600 ml-1">Very Low</span>
                        </div>
                        <div>
                          <span className="text-indigo-700 font-medium">Device Trust:</span>
                          <span className="text-green-600 ml-1">Verified</span>
                        </div>
                        <div>
                          <span className="text-indigo-700 font-medium">Network Security:</span>
                          <span className="text-green-600 ml-1">Secure</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Security Status Summary */}
              {isSecurityCompliant() ? (
                <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <CheckCircle className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-green-800">🛡️ Maximum Security Achieved!</p>
                      <p className="text-xs text-green-600">All critical security layers verified. Safe to withdraw.</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mt-6 p-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mr-3">
                      <AlertTriangle className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-red-800">⚠️ Security Enhancement Required</p>
                      <p className="text-xs text-red-600">{getSecurityRiskLevel().description}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Column - Withdrawal Form */}
          <div className="lg:col-span-2">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
              <div className="flex items-center mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Withdraw Funds</h2>
                  <p className="text-gray-600">Choose amount and payment method</p>
                </div>
              </div>

              <div className="space-y-8">
                {/* Step 1: Amount */}
                {currentStep >= 1 && (
                  <div className="space-y-4">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">1</div>
                      <h3 className="text-lg font-semibold text-gray-900">Enter Amount</h3>
                    </div>

                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Withdrawal Amount (USD)
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                          <DollarSign className="h-6 w-6 text-gray-400" />
                        </div>
                        <input
                          type="number"
                          min="100"
                          step="0.01"
                          value={amount}
                          onChange={(e) => setAmount(e.target.value)}
                          className="w-full pl-12 pr-4 py-4 text-xl font-semibold border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80 backdrop-blur-sm"
                          placeholder="100.00"
                        />
                      </div>
                      <div className="flex justify-between items-center mt-3">
                        <p className="text-sm text-gray-600">
                          Available: <span className="font-semibold text-green-600">${balance.availableBalance}</span>
                        </p>
                        <div className="flex space-x-2">
                          {[100, 500, 1000].map((preset) => (
                            <button
                              key={preset}
                              onClick={() => setAmount(preset.toString())}
                              className="px-3 py-1 text-xs bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                              ${preset}
                            </button>
                          ))}
                          <button
                            onClick={() => setAmount(balance.availableBalance.toString())}
                            className="px-3 py-1 text-xs bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                          >
                            Max
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100">
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        <Globe className="inline h-4 w-4 mr-2" />
                        Country/Region
                      </label>
                      <select
                        value={country}
                        onChange={(e) => setCountry(e.target.value)}
                        className="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white/80 backdrop-blur-sm text-lg"
                      >
                        {Object.entries(countries).map(([code, info]) => (
                          <option key={code} value={code}>
                            {info.flag} {info.name} (Tax: {info.tax}% | Fee: {info.serviceFee}%)
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}

                {/* Step 2: Payment Method */}
                {currentStep >= 2 && (
                  <div className="space-y-4">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">2</div>
                      <h3 className="text-lg font-semibold text-gray-900">Choose Payment Method</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(paymentMethods).map(([key, method]) => {
                        const Icon = method.icon;
                        const isSelected = withdrawalMethod === key;
                        return (
                          <button
                            key={key}
                            onClick={() => setWithdrawalMethod(key)}
                            className={`relative p-6 border-2 rounded-xl text-left transition-all duration-300 transform hover:scale-105 ${
                              isSelected
                                ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg'
                                : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                            }`}
                          >
                            {method.popular && (
                              <div className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                                Popular
                              </div>
                            )}

                            <div className="flex items-center mb-3">
                              <div className={`w-10 h-10 rounded-xl flex items-center justify-center mr-3 ${
                                isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'
                              }`}>
                                <Icon className="h-5 w-5" />
                              </div>
                              <div>
                                <span className="font-semibold text-gray-900">{method.name}</span>
                                {isSelected && <CheckCircle className="inline h-4 w-4 text-blue-500 ml-2" />}
                              </div>
                            </div>

                            <p className="text-sm text-gray-600 mb-3">{method.description}</p>

                            <div className="flex justify-between items-center text-sm">
                              <div className="flex items-center text-gray-500">
                                <Clock className="h-4 w-4 mr-1" />
                                {method.time}
                              </div>
                              <div className="flex items-center">
                                <span className="text-gray-500 mr-1">Fee:</span>
                                <span className="font-semibold text-gray-900">{method.fee}%</span>
                              </div>
                            </div>

                            <div className="mt-3 flex items-center justify-between">
                              <div className="flex items-center text-xs text-gray-500">
                                <Shield className="h-3 w-3 mr-1" />
                                {method.security} Security
                              </div>
                              {method.popular && (
                                <div className="flex items-center text-xs text-orange-600">
                                  <Star className="h-3 w-3 mr-1" />
                                  Recommended
                                </div>
                              )}
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Step 3: Account Information */}
                {currentStep >= 3 && withdrawalMethod && (
                  <div className="space-y-4">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">3</div>
                      <h3 className="text-lg font-semibold text-gray-900">Account Information</h3>
                    </div>

                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
                      {withdrawalMethod === 'bank_card' && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Bank Name *
                              </label>
                              <input
                                type="text"
                                value={accountInfo.bankName}
                                onChange={(e) => setAccountInfo(prev => ({ ...prev, bankName: e.target.value }))}
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                placeholder="Enter bank name"
                                required
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Account Holder Name *
                              </label>
                              <input
                                type="text"
                                value={accountInfo.accountHolderName}
                                onChange={(e) => setAccountInfo(prev => ({ ...prev, accountHolderName: e.target.value }))}
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                placeholder="Full name as on bank account"
                                required
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Account Number *
                              </label>
                              <input
                                type="text"
                                value={accountInfo.accountNumber}
                                onChange={(e) => setAccountInfo(prev => ({ ...prev, accountNumber: e.target.value }))}
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                placeholder="Enter account number"
                                required
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Routing Number *
                              </label>
                              <input
                                type="text"
                                value={accountInfo.routingNumber}
                                onChange={(e) => setAccountInfo(prev => ({ ...prev, routingNumber: e.target.value }))}
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                placeholder="Enter routing number"
                                required
                              />
                            </div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              SWIFT Code (for international transfers)
                            </label>
                            <input
                              type="text"
                              value={accountInfo.swiftCode}
                              onChange={(e) => setAccountInfo(prev => ({ ...prev, swiftCode: e.target.value }))}
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                              placeholder="Enter SWIFT code (optional)"
                            />
                          </div>
                        </div>
                      )}

                      {withdrawalMethod === 'paypal' && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            PayPal Email Address *
                          </label>
                          <input
                            type="email"
                            value={accountInfo.paypalEmail}
                            onChange={(e) => setAccountInfo(prev => ({ ...prev, paypalEmail: e.target.value }))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            placeholder="Enter your PayPal email"
                            required
                          />
                          <p className="text-sm text-gray-600 mt-2">
                            Make sure this email is associated with your verified PayPal account.
                          </p>
                        </div>
                      )}

                      {withdrawalMethod === 'crypto' && (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Cryptocurrency Network *
                            </label>
                            <select
                              value={accountInfo.cryptoNetwork}
                              onChange={(e) => setAccountInfo(prev => ({ ...prev, cryptoNetwork: e.target.value }))}
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            >
                              <option value="BTC">Bitcoin (BTC)</option>
                              <option value="ETH">Ethereum (ETH)</option>
                              <option value="USDT">Tether (USDT)</option>
                              <option value="USDC">USD Coin (USDC)</option>
                            </select>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Wallet Address *
                            </label>
                            <input
                              type="text"
                              value={accountInfo.cryptoAddress}
                              onChange={(e) => setAccountInfo(prev => ({ ...prev, cryptoAddress: e.target.value }))}
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                              placeholder="Enter your wallet address"
                              required
                            />
                            <p className="text-sm text-red-600 mt-2">
                              ⚠️ Double-check your wallet address. Incorrect addresses may result in permanent loss of funds.
                            </p>
                          </div>
                        </div>
                      )}

                      {withdrawalMethod === 'wise' && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Wise Email Address *
                          </label>
                          <input
                            type="email"
                            value={accountInfo.wiseEmail}
                            onChange={(e) => setAccountInfo(prev => ({ ...prev, wiseEmail: e.target.value }))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            placeholder="Enter your Wise account email"
                            required
                          />
                        </div>
                      )}

                      {withdrawalMethod === 'alipay' && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Alipay Account *
                          </label>
                          <input
                            type="text"
                            value={accountInfo.alipayAccount}
                            onChange={(e) => setAccountInfo(prev => ({ ...prev, alipayAccount: e.target.value }))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            placeholder="Enter your Alipay account (phone or email)"
                            required
                          />
                        </div>
                      )}

                      {withdrawalMethod === 'wechat' && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            WeChat Account *
                          </label>
                          <input
                            type="text"
                            value={accountInfo.wechatAccount}
                            onChange={(e) => setAccountInfo(prev => ({ ...prev, wechatAccount: e.target.value }))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            placeholder="Enter your WeChat ID"
                            required
                          />
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-4 pt-6">
                  {currentStep > 1 && (
                    <button
                      onClick={() => setCurrentStep(currentStep - 1)}
                      className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-all font-medium"
                    >
                      Back
                    </button>
                  )}

                  {currentStep < 3 ? (
                    <button
                      onClick={() => setCurrentStep(currentStep + 1)}
                      disabled={currentStep === 2 && !withdrawalMethod}
                      className={`flex-1 py-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center ${
                        (currentStep === 1 && amount) || (currentStep === 2 && withdrawalMethod)
                          ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:scale-105'
                          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      <ArrowRight className="h-5 w-5 mr-2" />
                      Next Step
                    </button>
                  ) : (
                    <button
                      onClick={handlePreview}
                      disabled={!isFullyVerified() || !amount || !isAccountInfoComplete()}
                      className={`flex-1 py-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center ${
                        isFullyVerified() && amount && isAccountInfoComplete()
                          ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:scale-105'
                          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      <ArrowRight className="h-5 w-5 mr-2" />
                      Preview Withdrawal
                    </button>
                  )}
                </div>

                {/* Verification Warning */}
                {!isFullyVerified() && (
                  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-6">
                    <div className="flex items-start">
                      <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center mr-4">
                        <AlertTriangle className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="text-lg font-semibold text-yellow-800 mb-2">
                          Verification Required
                        </p>
                        <p className="text-sm text-yellow-700 mb-3">
                          Complete all verification steps to enable withdrawals. This helps protect your account and ensures secure transactions.
                        </p>
                        <button
                          onClick={() => setCurrentStep(3)}
                          className="text-sm bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors flex items-center"
                        >
                          <Shield className="w-4 h-4 mr-2" />
                          Start Verification
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Withdrawal Preview */}
                {preview && (
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                    <div className="flex items-center mb-6">
                      <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3">
                        <CheckCircle className="h-5 w-5 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900">Withdrawal Summary</h3>
                    </div>

                    <div className="space-y-4">
                      <div className="flex justify-between items-center py-2">
                        <span className="text-gray-600">Withdrawal Amount:</span>
                        <span className="font-semibold text-xl text-gray-900">${preview.amount}</span>
                      </div>

                      <div className="bg-white/60 rounded-lg p-4 space-y-2">
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-red-600">Tax ({(preview.taxRate * 100).toFixed(1)}%):</span>
                          <span className="text-red-600 font-medium">-${preview.taxAmount}</span>
                        </div>
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-orange-600">Service Fee ({countries[country as keyof typeof countries].serviceFee}%):</span>
                          <span className="text-orange-600 font-medium">-${preview.serviceFeeAmount}</span>
                        </div>
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-red-600">Method Fee ({paymentMethods[withdrawalMethod as keyof typeof paymentMethods].fee}%):</span>
                          <span className="text-red-600 font-medium">-${preview.methodFeeAmount}</span>
                        </div>
                      </div>

                      <div className="border-t border-green-200 pt-4">
                        <div className="flex justify-between items-center">
                          <span className="text-lg font-semibold text-gray-900">You'll Receive:</span>
                          <span className="text-2xl font-bold text-green-600">${preview.netAmount}</span>
                        </div>
                        <p className="text-sm text-gray-600 mt-2">
                          Estimated arrival: {preview.estimatedArrival} via {paymentMethods[withdrawalMethod as keyof typeof paymentMethods].name}
                        </p>
                      </div>

                      <button
                        onClick={handleSecurityVerification}
                        className="w-full mt-6 bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all font-semibold flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105"
                      >
                        <Shield className="h-5 w-5 mr-2" />
                        Proceed to Security Verification
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Additional Info Section */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mr-3">
                <Shield className="h-5 w-5 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Secure Process</h3>
            </div>
            <p className="text-sm text-gray-600">
              All withdrawals are protected by bank-level security and multi-factor authentication.
            </p>
          </div>

          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mr-3">
                <Clock className="h-5 w-5 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Fast Processing</h3>
            </div>
            <p className="text-sm text-gray-600">
              Most withdrawals are processed within 24 hours on business days.
            </p>
          </div>

          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center mr-3">
                <Smartphone className="h-5 w-5 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900">24/7 Support</h3>
            </div>
            <p className="text-sm text-gray-600">
              Our support team is available around the clock to help with any questions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function WithdrawPage() {
  return (
    <AuthGuard requireAuth={true} redirectTo="/auth/login">
      <WithdrawContent />
    </AuthGuard>
  );
}