'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import WorkCard from '@/components/WorkCard';
import LoadingState from '@/components/ui/LoadingState';
import { Work } from '@/types';
import { mockWorks } from '@/data/mockWorks';
import { SearchFilters } from '@/components/AdvancedSearchModal';

function SearchPageContent() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';

  const [results, setResults] = useState<Work[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalResults, setTotalResults] = useState(0);
  const [activeFilters, setActiveFilters] = useState<SearchFilters>({
    query: query,
    type: (searchParams.get('type') as any) || 'all',
    category: searchParams.get('category') || 'all',
    author: searchParams.get('author') || '',
    dateRange: (searchParams.get('dateRange') as any) || 'all',
    sortBy: (searchParams.get('sortBy') as any) || 'relevance',
    minViews: parseInt(searchParams.get('minViews') || '0'),
    tags: searchParams.get('tags')?.split(',').filter(Boolean) || []
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  useEffect(() => {
    if (query) {
      fetchSearchResults();
    }
  }, [query, activeFilters]);

  const fetchSearchResults = async () => {
    try {
      setLoading(true);

      // 获取用户创建的内容
      const userContent = JSON.parse(localStorage.getItem('userContent') || '[]');

      // 将用户内容转换为Work格式
      const userWorks: Work[] = userContent.map((content: any) => ({
        id: content.id.toString(),
        title: content.title,
        description: content.description || content.content?.substring(0, 200) + '...' || '',
        author: {
          id: content.authorId || 'user',
          name: content.author || 'User',
          avatar: '/default-avatar.png',
          verified: false
        },
        type: content.contentType || 'article',
        category: content.category || 'general',
        tags: content.tags ? content.tags.split(',').map((tag: string) => tag.trim()) : [],
        views: content.views || 0,
        likes: content.likes || 0,
        comments: content.comments || 0,
        publishedAt: content.publishedAt || content.createdAt,
        thumbnail: content.coverImage || '/default-thumbnail.jpg',
        duration: content.contentType === 'video' ? '5:00' : undefined,
        readTime: content.contentType === 'article' ? '5 min read' : undefined
      }));

      // 合并mock数据和用户创建的内容
      const allWorks = [...mockWorks, ...userWorks];

      // 高级搜索功能
      const filteredResults = allWorks.filter(work => {
        // 基础查询匹配
        const matchesQuery = !activeFilters.query || 
          work.title.toLowerCase().includes(activeFilters.query.toLowerCase()) ||
          work.description.toLowerCase().includes(activeFilters.query.toLowerCase()) ||
          work.author.name.toLowerCase().includes(activeFilters.query.toLowerCase()) ||
          work.tags.some(tag => tag.toLowerCase().includes(activeFilters.query.toLowerCase()));
        
        // 内容类型过滤
        const matchesType = activeFilters.type === 'all' || work.type === activeFilters.type.slice(0, -1);
        
        // 分类过滤
        const matchesCategory = activeFilters.category === 'all' || work.category.toLowerCase() === activeFilters.category.toLowerCase();
        
        // 作者过滤
        const matchesAuthor = !activeFilters.author || work.author.name.toLowerCase().includes(activeFilters.author.toLowerCase());
        
        // 最小浏览量过滤
        const matchesMinViews = work.views >= activeFilters.minViews;
        
        // 标签过滤
        const matchesTags = activeFilters.tags.length === 0 || 
          activeFilters.tags.some(filterTag => 
            work.tags.some(workTag => workTag.toLowerCase().includes(filterTag.toLowerCase()))
          );
        
        // 日期过滤
        let matchesDate = true;
        if (activeFilters.dateRange !== 'all') {
          const publishDate = new Date(work.publishedAt);
          const now = new Date();
          const diffTime = now.getTime() - publishDate.getTime();
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
          switch (activeFilters.dateRange) {
            case 'today':
              matchesDate = diffDays <= 1;
              break;
            case 'week':
              matchesDate = diffDays <= 7;
              break;
            case 'month':
              matchesDate = diffDays <= 30;
              break;
            case 'year':
              matchesDate = diffDays <= 365;
              break;
          }
        }
        
        return matchesQuery && matchesType && matchesCategory && matchesAuthor && matchesMinViews && matchesTags && matchesDate;
      });
      
      // 排序结果
      const sortedResults = [...filteredResults].sort((a, b) => {
        switch (activeFilters.sortBy) {
          case 'date':
            return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
          case 'views':
            return b.views - a.views;
          case 'likes':
            return b.likes - a.likes;
          case 'relevance':
          default:
            // 增强的相关性排序
            let aScore = 0;
            let bScore = 0;
            
            if (activeFilters.query) {
              // 标题匹配权重最高
              if (a.title.toLowerCase().includes(activeFilters.query.toLowerCase())) aScore += 10;
              if (b.title.toLowerCase().includes(activeFilters.query.toLowerCase())) bScore += 10;
              
              // 描述匹配权重中等
              if (a.description.toLowerCase().includes(activeFilters.query.toLowerCase())) aScore += 5;
              if (b.description.toLowerCase().includes(activeFilters.query.toLowerCase())) bScore += 5;
              
              // 标签匹配权重较低
              if (a.tags.some(tag => tag.toLowerCase().includes(activeFilters.query.toLowerCase()))) aScore += 3;
              if (b.tags.some(tag => tag.toLowerCase().includes(activeFilters.query.toLowerCase()))) bScore += 3;
              
              // 作者匹配权重最低
              if (a.author.name.toLowerCase().includes(activeFilters.query.toLowerCase())) aScore += 1;
              if (b.author.name.toLowerCase().includes(activeFilters.query.toLowerCase())) bScore += 1;
            }
            
            // 如果相关性分数相同，按浏览量排序
            if (aScore === bScore) {
              return b.views - a.views;
            }
            
            return bScore - aScore;
        }
      });
      
      setResults(sortedResults);
      setTotalResults(sortedResults.length);
    } catch (error) {
      console.error('Error fetching search results:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Results Header */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Search Results</h1>
              {query && (
                <p className="text-gray-600">
                  {loading ? (
                    <LoadingState isLoading={true} loadingText="Searching..." size="sm" />
                  ) : (
                    <>
                      {totalResults} result{totalResults !== 1 ? 's' : ''} found for "{query}"
                    </>
                  )}
                </p>
              )}
            </div>
            
            {/* Active Filters Display */}
            <div className="flex flex-wrap gap-2">
              {activeFilters.type !== 'all' && (
                <span className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                  Type: {activeFilters.type}
                  <button
                    onClick={() => setActiveFilters(prev => ({ ...prev, type: 'all' }))}
                    className="ml-2 hover:text-blue-600"
                  >
                    ×
                  </button>
                </span>
              )}
              {activeFilters.category !== 'all' && (
                <span className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                  Category: {activeFilters.category}
                  <button
                    onClick={() => setActiveFilters(prev => ({ ...prev, category: 'all' }))}
                    className="ml-2 hover:text-green-600"
                  >
                    ×
                  </button>
                </span>
              )}
              {activeFilters.author && (
                <span className="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">
                  Author: {activeFilters.author}
                  <button
                    onClick={() => setActiveFilters(prev => ({ ...prev, author: '' }))}
                    className="ml-2 hover:text-purple-600"
                  >
                    ×
                  </button>
                </span>
              )}
              {activeFilters.dateRange !== 'all' && (
                <span className="inline-flex items-center px-3 py-1 bg-orange-100 text-orange-800 text-sm rounded-full">
                  Date: {activeFilters.dateRange}
                  <button
                    onClick={() => setActiveFilters(prev => ({ ...prev, dateRange: 'all' }))}
                    className="ml-2 hover:text-orange-600"
                  >
                    ×
                  </button>
                </span>
              )}
              {activeFilters.minViews > 0 && (
                <span className="inline-flex items-center px-3 py-1 bg-red-100 text-red-800 text-sm rounded-full">
                  Min Views: {activeFilters.minViews}+
                  <button
                    onClick={() => setActiveFilters(prev => ({ ...prev, minViews: 0 }))}
                    className="ml-2 hover:text-red-600"
                  >
                    ×
                  </button>
                </span>
              )}
              {activeFilters.tags.map(tag => (
                <span key={tag} className="inline-flex items-center px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">
                  Tag: {tag}
                  <button
                    onClick={() => setActiveFilters(prev => ({ 
                      ...prev, 
                      tags: prev.tags.filter(t => t !== tag) 
                    }))}
                    className="ml-2 hover:text-yellow-600"
                  >
                    ×
                  </button>
                </span>
              ))}
              {activeFilters.sortBy !== 'relevance' && (
                <span className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full">
                  Sort: {activeFilters.sortBy}
                  <button
                    onClick={() => setActiveFilters(prev => ({ ...prev, sortBy: 'relevance' }))}
                    className="ml-2 hover:text-gray-600"
                  >
                    ×
                  </button>
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Search Results */}
        {loading ? (
          <div className="space-y-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg p-6 animate-pulse">
                <div className="flex gap-6">
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-3"></div>
                    <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
                    <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                  <div className="w-64 h-40 bg-gray-200 rounded-lg"></div>
                </div>
              </div>
            ))}
          </div>
        ) : results.length > 0 ? (
          <div className="space-y-6">
            {results.map((work) => (
              <WorkCard
                key={work.id}
                work={work}
                layout="horizontal"
                showImage={true}
                showAuthor={true}
                showStats={true}
                showInteractions={true}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <svg
              className="mx-auto h-24 w-24 text-gray-300 mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
            <h3 className="text-xl font-medium text-gray-900 mb-2">No results found</h3>
            <p className="text-gray-600 mb-6">
              We couldn't find any content matching your search criteria. Try adjusting your filters or using different keywords.
            </p>

            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Popular Categories:</h4>
                <div className="flex flex-wrap justify-center gap-2">
                  {['Technology', 'Travel', 'Lifestyle', 'Food'].map((category) => (
                    <button
                      key={category}
                      onClick={() => setActiveFilters(prev => ({ ...prev, category, query: '' }))}
                      className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full hover:bg-blue-200 transition-colors"
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Search Tips:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Try different or more general keywords</li>
                  <li>• Check your spelling</li>
                  <li>• Use fewer keywords or remove filters</li>
                  <li>• Browse our featured content instead</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={<LoadingState />}>
      <SearchPageContent />
    </Suspense>
  );
}
