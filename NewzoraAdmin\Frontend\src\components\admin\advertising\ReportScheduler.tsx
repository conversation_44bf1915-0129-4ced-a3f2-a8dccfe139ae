'use client';

import React, { useState, useEffect } from 'react';
import { 
  Clock, Calendar, Mail, Settings, Play, Pause, 
  Trash2, Edit, Plus, CheckCircle, AlertCircle 
} from 'lucide-react';

interface ScheduledReport {
  id: string;
  campaignId: string;
  campaignName: string;
  reportType: 'summary' | 'detailed' | 'audience' | 'performance';
  format: 'pdf' | 'excel' | 'csv';
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string;
  recipients: string[];
  isActive: boolean;
  lastSent?: string;
  nextSend: string;
  createdAt: string;
}

interface ReportSchedulerProps {
  onClose: () => void;
}

/**
 * 广告报告调度器组件
 * 管理定期自动生成和发送广告报告
 */
const ReportScheduler: React.FC<ReportSchedulerProps> = ({ onClose }) => {
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingReport, setEditingReport] = useState<ScheduledReport | null>(null);
  const [loading, setLoading] = useState(true);

  // 新报告表单数据
  const [newReport, setNewReport] = useState({
    campaignId: '',
    reportType: 'summary' as const,
    format: 'pdf' as const,
    frequency: 'weekly' as const,
    time: '09:00',
    recipients: ['']
  });

  // 模拟活动数据
  const campaigns = [
    { id: '1', name: 'Tech Product Launch Campaign' },
    { id: '2', name: 'Holiday Sales Promotion' },
    { id: '3', name: 'Brand Awareness Campaign' }
  ];

  useEffect(() => {
    loadScheduledReports();
  }, []);

  const loadScheduledReports = async () => {
    try {
      // 模拟加载调度报告数据
      const mockReports: ScheduledReport[] = [
        {
          id: '1',
          campaignId: '1',
          campaignName: 'Tech Product Launch Campaign',
          reportType: 'summary',
          format: 'pdf',
          frequency: 'weekly',
          time: '09:00',
          recipients: ['<EMAIL>', '<EMAIL>'],
          isActive: true,
          lastSent: '2024-01-15T09:00:00Z',
          nextSend: '2024-01-22T09:00:00Z',
          createdAt: '2024-01-01T10:00:00Z'
        },
        {
          id: '2',
          campaignId: '2',
          campaignName: 'Holiday Sales Promotion',
          reportType: 'detailed',
          format: 'excel',
          frequency: 'daily',
          time: '08:00',
          recipients: ['<EMAIL>'],
          isActive: false,
          lastSent: '2024-01-14T08:00:00Z',
          nextSend: '2024-01-16T08:00:00Z',
          createdAt: '2024-01-10T15:30:00Z'
        }
      ];

      setScheduledReports(mockReports);
    } catch (error) {
      console.error('Failed to load scheduled reports:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddReport = async () => {
    if (!newReport.campaignId || newReport.recipients.some(email => !email.trim())) {
      alert('Please fill in all required fields');
      return;
    }

    const campaign = campaigns.find(c => c.id === newReport.campaignId);
    if (!campaign) return;

    const report: ScheduledReport = {
      id: Date.now().toString(),
      campaignId: newReport.campaignId,
      campaignName: campaign.name,
      reportType: newReport.reportType,
      format: newReport.format,
      frequency: newReport.frequency,
      time: newReport.time,
      recipients: newReport.recipients.filter(email => email.trim()),
      isActive: true,
      nextSend: calculateNextSend(newReport.frequency, newReport.time),
      createdAt: new Date().toISOString()
    };

    setScheduledReports(prev => [...prev, report]);
    setShowAddForm(false);
    setNewReport({
      campaignId: '',
      reportType: 'summary',
      format: 'pdf',
      frequency: 'weekly',
      time: '09:00',
      recipients: ['']
    });
  };

  const calculateNextSend = (frequency: string, time: string): string => {
    const now = new Date();
    const [hours, minutes] = time.split(':').map(Number);
    
    let nextSend = new Date();
    nextSend.setHours(hours, minutes, 0, 0);
    
    if (nextSend <= now) {
      nextSend.setDate(nextSend.getDate() + 1);
    }
    
    switch (frequency) {
      case 'weekly':
        nextSend.setDate(nextSend.getDate() + (7 - nextSend.getDay()));
        break;
      case 'monthly':
        nextSend.setMonth(nextSend.getMonth() + 1, 1);
        break;
    }
    
    return nextSend.toISOString();
  };

  const toggleReportStatus = (reportId: string) => {
    setScheduledReports(prev => 
      prev.map(report => 
        report.id === reportId 
          ? { ...report, isActive: !report.isActive }
          : report
      )
    );
  };

  const deleteReport = (reportId: string) => {
    if (confirm('Are you sure you want to delete this scheduled report?')) {
      setScheduledReports(prev => prev.filter(report => report.id !== reportId));
    }
  };

  const addRecipient = () => {
    setNewReport(prev => ({
      ...prev,
      recipients: [...prev.recipients, '']
    }));
  };

  const updateRecipient = (index: number, email: string) => {
    setNewReport(prev => ({
      ...prev,
      recipients: prev.recipients.map((recipient, i) => 
        i === index ? email : recipient
      )
    }));
  };

  const removeRecipient = (index: number) => {
    setNewReport(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }));
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-2xl shadow-2xl p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-4">Loading scheduled reports...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                <Clock className="w-6 h-6 mr-2 text-blue-600" />
                Report Scheduler
              </h2>
              <p className="text-gray-600 mt-1">Manage automated report generation and delivery</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* 添加新报告按钮 */}
          <div className="mb-6">
            <button
              onClick={() => setShowAddForm(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Schedule New Report
            </button>
          </div>

          {/* 调度报告列表 */}
          <div className="space-y-4">
            {scheduledReports.map((report) => (
              <div key={report.id} className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{report.campaignName}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        report.isActive 
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {report.isActive ? 'Active' : 'Paused'}
                      </span>
                      <span>{report.reportType.toUpperCase()}</span>
                      <span>{report.format.toUpperCase()}</span>
                      <span>{report.frequency} at {report.time}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => toggleReportStatus(report.id)}
                      className={`p-2 rounded-lg transition-colors ${
                        report.isActive
                          ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                          : 'bg-green-100 text-green-700 hover:bg-green-200'
                      }`}
                    >
                      {report.isActive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                    </button>
                    <button
                      onClick={() => setEditingReport(report)}
                      className="p-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => deleteReport(report.id)}
                      className="p-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Recipients:</span>
                    <div className="mt-1">
                      {report.recipients.map((email, index) => (
                        <div key={index} className="text-gray-600">{email}</div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Last Sent:</span>
                    <div className="text-gray-600 mt-1">
                      {report.lastSent ? new Date(report.lastSent).toLocaleString() : 'Never'}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Next Send:</span>
                    <div className="text-gray-600 mt-1">
                      {new Date(report.nextSend).toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {scheduledReports.length === 0 && (
              <div className="text-center py-12">
                <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Scheduled Reports</h3>
                <p className="text-gray-600">Create your first scheduled report to get started.</p>
              </div>
            )}
          </div>
        </div>

        {/* 添加报告表单 */}
        {showAddForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
            <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-900">Schedule New Report</h3>
              </div>
              
              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Campaign</label>
                  <select
                    value={newReport.campaignId}
                    onChange={(e) => setNewReport(prev => ({ ...prev, campaignId: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select Campaign</option>
                    {campaigns.map(campaign => (
                      <option key={campaign.id} value={campaign.id}>{campaign.name}</option>
                    ))}
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                    <select
                      value={newReport.reportType}
                      onChange={(e) => setNewReport(prev => ({ ...prev, reportType: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="summary">Summary</option>
                      <option value="detailed">Detailed</option>
                      <option value="audience">Audience</option>
                      <option value="performance">Performance</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Format</label>
                    <select
                      value={newReport.format}
                      onChange={(e) => setNewReport(prev => ({ ...prev, format: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="pdf">PDF</option>
                      <option value="excel">Excel</option>
                      <option value="csv">CSV</option>
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Frequency</label>
                    <select
                      value={newReport.frequency}
                      onChange={(e) => setNewReport(prev => ({ ...prev, frequency: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Time</label>
                    <input
                      type="time"
                      value={newReport.time}
                      onChange={(e) => setNewReport(prev => ({ ...prev, time: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Recipients</label>
                  {newReport.recipients.map((email, index) => (
                    <div key={index} className="flex items-center space-x-2 mb-2">
                      <input
                        type="email"
                        value={email}
                        onChange={(e) => updateRecipient(index, e.target.value)}
                        placeholder="Enter email address"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      />
                      {newReport.recipients.length > 1 && (
                        <button
                          onClick={() => removeRecipient(index)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    onClick={addRecipient}
                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Add Recipient
                  </button>
                </div>
              </div>

              <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button
                  onClick={() => setShowAddForm(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddReport}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Schedule Report
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportScheduler;
