import type { Metadata } from 'next';
import { Inter, Space_Grotesk, JetBrains_Mono } from 'next/font/google';
import './globals.css';
import ClientLayout from '@/components/ClientLayout';

// Primary font for body text
const inter = Inter({
  variable: '--font-inter',
  subsets: ['latin'],
  display: 'swap',
});

// Modern font for headings and brand
const spaceGrotesk = Space_Grotesk({
  variable: '--font-space-grotesk',
  subsets: ['latin'],
  display: 'swap',
});

// Monospace font for code
const jetbrainsMono = JetBrains_Mono({
  variable: '--font-jetbrains-mono',
  subsets: ['latin'],
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'Newzora - Your Gateway to Quality Content',
  description:
    'Discover amazing articles on technology, lifestyle, travel, food and more. Your trusted source for quality news and content.',
  other: {
    google: 'translate',
    'google-translate-customization': 'auto',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="google" content="translate" />
        <meta name="google-translate-customization" content="auto" />
        <meta name="google-site-verification" content="notranslate" />
        <style dangerouslySetInnerHTML={{
          __html: `
            .notranslate { -webkit-transform: none !important; }
            [translate="no"] { -webkit-transform: none !important; }
            .brand-name { -webkit-transform: none !important; transform: none !important; }
            .goog-te-banner-frame { display: none !important; }
          `
        }} />
      </head>
      <body
        className={`${inter.variable} ${spaceGrotesk.variable} ${jetbrainsMono.variable} font-inter antialiased`}
        suppressHydrationWarning
      >
        <ClientLayout>
          {children}
        </ClientLayout>
        {/* 新的分离翻译系统：PC端浏览器翻译，移动端系统翻译 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 初始化分离翻译系统
              (function() {
                if (typeof window !== 'undefined') {
                  // 动态导入翻译管理器
                  import('/src/utils/translationManager.js').then(module => {
                    const { translationManager } = module;
                    translationManager.initialize().catch(console.error);
                  }).catch(() => {
                    // 降级处理：直接初始化基本翻译功能
                    console.log('Using fallback translation initialization');
                  });
                }
              })();
            `,
          }}
        />
      </body>
    </html>
  );
}