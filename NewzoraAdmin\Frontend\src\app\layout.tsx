import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/contexts/AuthContext'
import SafeRender from '@/components/SafeRender'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Newzora Admin',
  description: 'Newzora Admin Management System',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <SafeRender>
          <AuthProvider>
            {children}
          </AuthProvider>
        </SafeRender>
      </body>
    </html>
  )
}