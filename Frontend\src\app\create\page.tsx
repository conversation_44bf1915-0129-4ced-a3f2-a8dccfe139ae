'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import Header from '@/components/Header';
import AuthGuard from '@/components/AuthGuard';
import EnhancedRichTextEditor from '@/components/EnhancedRichTextEditor';
import MediaUploader from '@/components/MediaUploader';
import QuickTips from '@/components/QuickTips';
import AICreationAssistant from '@/components/AICreationAssistant';
import {
  Video,
  Mic,
  Image as ImageIcon,
  FileText,
  Radio,
  Settings,
  Eye,
  Save,
  Send,
  Palette,
  Layers,
  Zap,
  Camera,
  Monitor,
  Smartphone,
  Users,
  Clock,
  TrendingUp,
  Heart,
  MessageCircle,
  Share2,
  BookOpen,
  Tag,
  Globe,
  Lock,
  Calendar,
  Play,
  Pause,
  Square,
  RotateCcw
} from 'lucide-react';

function CreateContent() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading } = useSimpleAuth();
  const [contentType, setContentType] = useState<'article' | 'video' | 'audio' | 'live'>('article');
  const [editId, setEditId] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // 直播功能状态
  const [isLiveMode, setIsLiveMode] = useState(false);
  const [liveStatus, setLiveStatus] = useState<'preparing' | 'live' | 'ended'>('preparing');
  const [liveViewers, setLiveViewers] = useState(0);
  const [liveComments, setLiveComments] = useState<Array<{id: string, user: string, message: string, timestamp: Date}>>([]);
  const [streamKey, setStreamKey] = useState('');
  const [liveTitle, setLiveTitle] = useState('');
  const [liveDescription, setLiveDescription] = useState('');
  const [selectedCamera, setSelectedCamera] = useState<string>('');
  const [selectedMicrophone, setSelectedMicrophone] = useState<string>('');
  const [availableDevices, setAvailableDevices] = useState<{cameras: MediaDeviceInfo[], microphones: MediaDeviceInfo[]}>({cameras: [], microphones: []});
  const videoPreviewRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // 封面设置状态
  const [showCoverSelector, setShowCoverSelector] = useState(false);
  const [customCover, setCustomCover] = useState<File | null>(null);
  const [coverPreview, setCoverPreview] = useState<string>('');
  const [coverType, setCoverType] = useState<'auto' | 'custom' | 'template'>('auto');

  // 检查是否为编辑模式
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const editParam = urlParams.get('edit');
    const typeParam = urlParams.get('type') as 'article' | 'video' | 'audio' | 'live';

    if (editParam) {
      setEditId(editParam);
      setIsEditMode(true);
      if (typeParam) {
        setContentType(typeParam);
        if (typeParam === 'live') {
          setIsLiveMode(true);
        }
      }
      loadEditData(editParam);
    }
  }, []);

  // 初始化媒体设备
  useEffect(() => {
    if (contentType === 'live' || isLiveMode) {
      initializeMediaDevices();
    }
  }, [contentType, isLiveMode]);

  // 初始化媒体设备
  const initializeMediaDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const cameras = devices.filter(device => device.kind === 'videoinput');
      const microphones = devices.filter(device => device.kind === 'audioinput');

      setAvailableDevices({ cameras, microphones });

      if (cameras.length > 0) setSelectedCamera(cameras[0].deviceId);
      if (microphones.length > 0) setSelectedMicrophone(microphones[0].deviceId);
    } catch (error) {
      console.error('Error accessing media devices:', error);
    }
  };

  // 开始直播预览
  const startLivePreview = async () => {
    try {
      const constraints = {
        video: selectedCamera ? { deviceId: selectedCamera } : true,
        audio: selectedMicrophone ? { deviceId: selectedMicrophone } : true
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;

      if (videoPreviewRef.current) {
        videoPreviewRef.current.srcObject = stream;
      }
    } catch (error) {
      console.error('Error starting live preview:', error);
      alert('无法访问摄像头或麦克风，请检查设备权限');
    }
  };

  // 停止直播预览
  const stopLivePreview = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    if (videoPreviewRef.current) {
      videoPreviewRef.current.srcObject = null;
    }
  };

  // 开始直播
  const startLiveStream = async () => {
    if (!liveTitle.trim()) {
      alert('请输入直播标题');
      return;
    }

    try {
      await startLivePreview();
      setLiveStatus('live');
      setStreamKey(`live_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

      // 模拟观众数量增长
      const viewerInterval = setInterval(() => {
        setLiveViewers(prev => prev + Math.floor(Math.random() * 3));
      }, 5000);

      // 清理函数
      return () => clearInterval(viewerInterval);
    } catch (error) {
      console.error('Error starting live stream:', error);
    }
  };

  // 结束直播
  const endLiveStream = () => {
    stopLivePreview();
    setLiveStatus('ended');
    setLiveViewers(0);
  };

  // 封面处理函数
  const handleCoverUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setCustomCover(file);
      setCoverType('custom');

      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setCoverPreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // 生成自动封面
  const generateAutoCover = () => {
    setCoverType('auto');
    // 这里可以基于内容生成封面
    setCoverPreview('/api/placeholder/600/400');
  };

  // 选择模板封面
  const selectTemplateCover = (templateUrl: string) => {
    setCoverType('template');
    setCoverPreview(templateUrl);
  };

  const loadEditData = (id: string) => {
    console.log('Loading edit data for ID:', id);

    // 首先尝试从用户创建的内容中加载
    try {
      const userContent = JSON.parse(localStorage.getItem('userContent') || '[]');
      const userItem = userContent.find((item: any) => String(item.id) === String(id));

      if (userItem) {
        console.log('Found user content:', userItem);
        setFormData(prev => ({
          ...prev,
          title: userItem.title || '',
          description: userItem.description || '',
          content: userItem.content || '',
          category: userItem.category || 'general',
          tags: userItem.tags || ''
        }));

        // 设置内容类型
        if (userItem.contentType) {
          setContentType(userItem.contentType);
        }

        // 设置封面
        if (userItem.coverImage) {
          setCoverPreview(userItem.coverImage);
          setCoverType(userItem.coverType || 'auto');
        }

        return;
      }
    } catch (error) {
      console.error('Error loading user content:', error);
    }

    // 如果用户内容中没有找到，尝试mock数据
    const mockDataMap: { [key: string]: any } = {
      '1': {
        title: 'The Future of AI in Everyday Life',
        description: 'Exploring how AI will transform our daily routines and interactions',
        content: '<h2>Introduction</h2><p>Artificial Intelligence is rapidly becoming an integral part of our daily lives...</p>',
        category: 'technology',
        tags: 'AI, artificial intelligence, technology, future'
      },
      '2': {
        title: 'Cybersecurity Best Practices',
        description: 'A comprehensive guide to staying safe online in 2024',
        content: '<h2>Introduction</h2><p>In today\'s digital age, cybersecurity has become more important than ever...</p>',
        category: 'technology',
        tags: 'cybersecurity, security, privacy, technology'
      },
      '3': {
        title: 'Travel Guide to Southeast Asia',
        description: 'Complete travel guide with tips and recommendations',
        content: 'Video content about Southeast Asia travel...',
        category: 'travel',
        tags: 'travel, asia, guide, tourism'
      },
      '4': {
        title: 'Meditation Music Collection',
        description: 'Relaxing sounds for meditation and focus',
        content: 'Audio content for meditation...',
        category: 'lifestyle',
        tags: 'meditation, music, relaxation, wellness'
      }
    };

    const data = mockDataMap[id];
    if (data) {
      console.log('Found mock data:', data);
      setFormData(prev => ({ ...prev, ...data }));
    } else {
      console.log('No data found for ID:', id);
    }
  };
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    category: '',
    tags: '',
    mediaFile: null as File | null,
    videoQuality: '1080p' as '480p' | '720p' | '1080p' | '1440p' | '2160p' | '4320p',
    audioBitrate: '320' as '128' | '192' | '256' | '320',
  });
  const [videoInfo, setVideoInfo] = useState<{
    duration: number;
    width: number;
    height: number;
    size: number;
  } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [aiReviewResult, setAiReviewResult] = useState<{
    passed: boolean;
    issues: string[];
    suggestions: string[];
  } | null>(null);
  const [showAiReview, setShowAiReview] = useState(false);

  // 使用与主页左侧栏相同的类别
  const categories = [
    { value: 'technology', label: 'Technology' },
    { value: 'finance', label: 'Finance' },
    { value: 'entertainment', label: 'Entertainment' },
    { value: 'lifestyle', label: 'Lifestyle' },
    { value: 'education', label: 'Education' },
    { value: 'health', label: 'Health & Fitness' },
    { value: 'travel', label: 'Travel' },
    { value: 'food', label: 'Food' },
    { value: 'sports', label: 'Sports' },
    { value: 'business', label: 'Business' },
    { value: 'science', label: 'Science' },
    { value: 'politics', label: 'Politics' },
    { value: 'history', label: 'History' },
    { value: 'news', label: 'News' },
  ];

  // Trending topics data - using actual mockWorks IDs and titles
  const trendingTopics = [
    { id: 1, title: 'The Future of Artificial Intelligence in 2024', category: 'Technology', views: '15.4K', trend: '+15%', type: 'article' },
    { id: 2, title: 'Sustainable Travel: A Guide to Eco-Friendly Adventures', category: 'Travel', views: '8.8K', trend: '+22%', type: 'article' },
    { id: 11, title: 'Quantum Computing: The Next Frontier', category: 'Science', views: '12.3K', trend: '+8%', type: 'article' },
    { id: 14, title: 'Startup Success: Lessons from Silicon Valley', category: 'Business', views: '16.7K', trend: '+12%', type: 'article' },
    { id: 21, title: 'Stock Market Analysis: Top Picks for 2024', category: 'Finance', views: '28.9K', trend: '+18%', type: 'article' },
  ];

  // Handle trending topic click - navigate to existing articles
  const handleTrendingTopicClick = (topic: typeof trendingTopics[0]) => {
    // Navigate to existing articles in mockWorks
    router.push(`/article/${topic.id}`);
  };

  // Quick Tips Component with collapsible content
  const QuickTipsCard = () => {
    const [expandedTip, setExpandedTip] = useState<number | null>(null);
    
    const tips = [
      {
        title: 'Use engaging titles to attract more readers',
        content: 'Create compelling headlines that clearly describe your content. Use action words, numbers, and emotional triggers. Keep titles under 60 characters for better SEO.'
      },
      {
        title: 'Add relevant tags to improve discoverability',
        content: 'Use 3-5 specific tags that accurately describe your content. Mix popular and niche tags. Research trending hashtags in your category for better reach.'
      },
      {
        title: 'Proofread your content before publishing',
        content: 'Check for grammar, spelling, and formatting errors. Read your content aloud to catch awkward phrasing. Use tools like Grammarly for additional help.'
      },
      {
        title: 'Use high-quality images and media',
        content: 'Choose clear, relevant images that support your content. Optimize file sizes for faster loading. Ensure you have proper rights to use all media.'
      }
    ];

    return (
      <div className="bg-white rounded-2xl border border-gray-200 shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">💡 Quick Tips</h2>
        <div className="space-y-3">
          {tips.map((tip, index) => (
            <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
              <button
                onClick={() => setExpandedTip(expandedTip === index ? null : index)}
                className="w-full flex items-start justify-between p-3 text-left hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start">
                  <span className="text-blue-500 mr-2 mt-1">•</span>
                  <span className="text-gray-700 text-sm font-medium">{tip.title}</span>
                </div>
                <svg 
                  className={`w-4 h-4 text-gray-400 transition-transform ${
                    expandedTip === index ? 'rotate-180' : ''
                  }`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {expandedTip === index && (
                <div className="px-3 pb-3 pl-8">
                  <p className="text-sm text-gray-600 leading-relaxed">{tip.content}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // AI Content Review System
  const runAiContentReview = async (content: string, title: string) => {
    const sensitiveKeywords = [
      'political sensitive',
      'violent content',
      'false information',
      'hate speech',
      'adult content',
      'fraud',
      'drugs',
      'weapons',
      'terrorism',
      'racial discrimination',
    ];

    const issues: string[] = [];
    const suggestions: string[] = [];

    // Simulate AI detection
    const fullText = `${title} ${content}`.toLowerCase();

    if (fullText.includes('political') || fullText.includes('government')) {
      issues.push('Political sensitive content detected');
      suggestions.push('Please avoid political topics, focus on technology or lifestyle content');
    }

    if (fullText.includes('violent') || fullText.includes('blood')) {
      issues.push('Violent content detected');
      suggestions.push('Please use more moderate expressions');
    }

    if (fullText.includes('fake') || fullText.includes('false')) {
      issues.push('Potentially false information detected');
      suggestions.push('Please ensure content is truthful and reliable, provide credible sources');
    }

    // Content quality check
    if (content.length < 100) {
      suggestions.push('Suggest increasing content length for more detailed information');
    }

    if (!title.trim()) {
      issues.push('Title cannot be empty');
    }

    return {
      passed: issues.length === 0,
      issues,
      suggestions,
    };
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleAiReview = async () => {
    setShowAiReview(true);
    const result = await runAiContentReview(formData.content, formData.title);
    setAiReviewResult(result);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // 直播模式特殊处理
      if (contentType === 'live' || isLiveMode) {
        if (liveStatus === 'live') {
          // 结束直播
          endLiveStream();
          alert('Live stream ended successfully!');
          router.push('/');
          return;
        } else {
          // 开始直播
          if (!liveTitle.trim()) {
            alert('Please enter a live stream title');
            setIsSubmitting(false);
            return;
          }

          await startLiveStream();
          alert('Live stream started successfully!');
          setIsSubmitting(false);
          return;
        }
      }

      // 常规内容提交流程
      const contentToReview = contentType === 'article' ? formData.content : formData.title;
      const reviewResult = await runAiContentReview(contentToReview, formData.title);

      if (!reviewResult.passed) {
        setAiReviewResult(reviewResult);
        setShowAiReview(true);
        setIsSubmitting(false);
        return;
      }

      // 准备提交数据
      const submitData = {
        ...formData,
        contentType,
        coverImage: coverPreview,
        coverType,
        publishedAt: new Date().toISOString(),
        status: 'draft', // 默认保存为草稿
        author: user?.username || 'Anonymous',
        authorId: user?.id || 'anonymous',
        views: 0,
        likes: 0,
        liveSettings: isLiveMode ? {
          title: liveTitle,
          description: liveDescription,
          streamKey,
          selectedCamera,
          selectedMicrophone
        } : null
      };

      console.log('Content submitted:', submitData);

      // 保存到本地存储（模拟数据库）
      const existingContent = JSON.parse(localStorage.getItem('userContent') || '[]');

      if (isEditMode && editId) {
        // 编辑模式：更新现有内容
        console.log('Updating existing content with ID:', editId);
        const updatedContent = existingContent.map((item: any) => {
          if (String(item.id) === String(editId)) {
            return {
              ...item,
              ...submitData,
              updatedAt: new Date().toISOString()
            };
          }
          return item;
        });
        localStorage.setItem('userContent', JSON.stringify(updatedContent));
      } else {
        // 创建模式：添加新内容
        console.log('Creating new content');
        const newContent = {
          id: Date.now(),
          ...submitData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        existingContent.push(newContent);
        localStorage.setItem('userContent', JSON.stringify(existingContent));
      }

      // 模拟网络延迟
      await new Promise((resolve) => setTimeout(resolve, 1500));

      if (isEditMode) {
        alert('Content updated successfully!');
        router.push('/content');
      } else {
        const successMessage = contentType === 'video'
          ? 'Video saved as draft successfully!'
          : contentType === 'audio'
          ? 'Audio saved as draft successfully!'
          : 'Content saved as draft successfully!';
        alert(successMessage);
        // 保存后跳转到content页面查看
        router.push('/content?tab=drafts');
      }
    } catch (error) {
      console.error('Error submitting content:', error);
      alert('An error occurred while submitting your content. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileUpload = (field: 'mediaFile', file: File) => {
    setFormData((prev) => ({ ...prev, [field]: file }));
  };

  const handleVideoInfo = (info: {
    duration: number;
    width: number;
    height: number;
    size: number;
  }) => {
    setVideoInfo(info);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex max-w-7xl mx-auto px-6 py-8 gap-8">
        {/* Main Content */}
        <main className="flex-1">
          <div className="mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4">
              {isEditMode ? 'Edit Content' : 'Create New Content'}
            </h1>
            <p className="text-lg text-gray-600">
              {isEditMode ? 'Update your content and republish when ready.' : 'Share your thoughts, insights, and stories with the world and inspire others.'}
            </p>
          </div>

          {/* Content Type Selection */}
          <div className="mb-8">
            <div className="bg-white rounded-2xl border border-gray-200 shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <Layers className="w-5 h-5 mr-2 text-blue-600" />
                Choose Content Type
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button
                  onClick={() => {
                    setContentType('article');
                    setIsLiveMode(false);
                  }}
                  className={`p-6 rounded-xl text-center transition-all duration-300 cursor-pointer border-2 ${
                    contentType === 'article'
                      ? 'bg-blue-50 border-blue-500 text-blue-700 shadow-lg transform scale-105'
                      : 'border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300'
                  }`}
                >
                  <FileText className="w-8 h-8 mx-auto mb-3" />
                  <div className="font-semibold">Article</div>
                  <div className="text-sm opacity-75">Write & Share</div>
                </button>

                <button
                  onClick={() => {
                    setContentType('video');
                    setIsLiveMode(false);
                  }}
                  className={`p-6 rounded-xl text-center transition-all duration-300 cursor-pointer border-2 ${
                    contentType === 'video'
                      ? 'bg-red-50 border-red-500 text-red-700 shadow-lg transform scale-105'
                      : 'border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300'
                  }`}
                >
                  <Video className="w-8 h-8 mx-auto mb-3" />
                  <div className="font-semibold">Video</div>
                  <div className="text-sm opacity-75">Upload & Edit</div>
                </button>

                <button
                  onClick={() => {
                    setContentType('audio');
                    setIsLiveMode(false);
                  }}
                  className={`p-6 rounded-xl text-center transition-all duration-300 cursor-pointer border-2 ${
                    contentType === 'audio'
                      ? 'bg-green-50 border-green-500 text-green-700 shadow-lg transform scale-105'
                      : 'border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300'
                  }`}
                >
                  <Mic className="w-8 h-8 mx-auto mb-3" />
                  <div className="font-semibold">Audio</div>
                  <div className="text-sm opacity-75">Podcast & Music</div>
                </button>

                <button
                  onClick={() => {
                    setContentType('live');
                    setIsLiveMode(true);
                    initializeMediaDevices();
                  }}
                  className={`p-6 rounded-xl text-center transition-all duration-300 cursor-pointer border-2 ${
                    contentType === 'live' || isLiveMode
                      ? 'bg-purple-50 border-purple-500 text-purple-700 shadow-lg transform scale-105'
                      : 'border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300'
                  }`}
                >
                  <Radio className="w-8 h-8 mx-auto mb-3" />
                  <div className="font-semibold">Live Stream</div>
                  <div className="text-sm opacity-75">Go Live Now</div>
                  {liveStatus === 'live' && (
                    <div className="mt-2 flex items-center justify-center">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-1"></div>
                      <span className="text-xs text-red-600 font-medium">LIVE</span>
                    </div>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Live Stream Setup */}
          {(contentType === 'live' || isLiveMode) && (
            <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl border border-purple-200 shadow-lg overflow-hidden mb-8">
              <div className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Radio className="w-6 h-6 mr-3" />
                    <div>
                      <h3 className="text-xl font-bold">Live Stream Studio</h3>
                      <p className="text-purple-100">Set up your live broadcast</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    {liveStatus === 'live' && (
                      <div className="flex items-center bg-red-500 px-3 py-1 rounded-full">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse mr-2"></div>
                        <span className="text-sm font-medium">LIVE</span>
                      </div>
                    )}
                    <div className="text-right">
                      <div className="text-sm text-purple-100">Viewers</div>
                      <div className="text-2xl font-bold">{liveViewers}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Live Preview */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                      <Camera className="w-5 h-5 mr-2 text-purple-600" />
                      Live Preview
                    </h4>
                    <div className="relative bg-black rounded-xl overflow-hidden aspect-video">
                      <video
                        ref={videoPreviewRef}
                        autoPlay
                        muted
                        className="w-full h-full object-cover"
                      />
                      {!streamRef.current && (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75">
                          <div className="text-center text-white">
                            <Camera className="w-12 h-12 mx-auto mb-4 opacity-50" />
                            <p className="text-lg font-medium">Camera Preview</p>
                            <p className="text-sm opacity-75">Click "Start Preview" to begin</p>
                          </div>
                        </div>
                      )}

                      {/* Live Controls Overlay */}
                      <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {liveStatus === 'preparing' && (
                            <button
                              type="button"
                              onClick={startLivePreview}
                              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center text-sm font-medium transition-colors"
                            >
                              <Play className="w-4 h-4 mr-2" />
                              Start Preview
                            </button>
                          )}

                          {liveStatus === 'preparing' && streamRef.current && (
                            <button
                              type="button"
                              onClick={startLiveStream}
                              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center text-sm font-medium transition-colors"
                            >
                              <Radio className="w-4 h-4 mr-2" />
                              Go Live
                            </button>
                          )}

                          {liveStatus === 'live' && (
                            <button
                              type="button"
                              onClick={endLiveStream}
                              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center text-sm font-medium transition-colors"
                            >
                              <Square className="w-4 h-4 mr-2" />
                              End Stream
                            </button>
                          )}
                        </div>

                        <div className="flex items-center space-x-2">
                          <button
                            type="button"
                            onClick={stopLivePreview}
                            className="bg-gray-600 hover:bg-gray-700 text-white p-2 rounded-lg transition-colors"
                            title="Stop Preview"
                          >
                            <Pause className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Live Settings */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                      <Settings className="w-5 h-5 mr-2 text-purple-600" />
                      Stream Settings
                    </h4>

                    {/* Device Selection */}
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Camera</label>
                        <select
                          value={selectedCamera}
                          onChange={(e) => setSelectedCamera(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        >
                          {availableDevices.cameras.map((camera) => (
                            <option key={camera.deviceId} value={camera.deviceId}>
                              {camera.label || `Camera ${camera.deviceId.slice(0, 8)}`}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Microphone</label>
                        <select
                          value={selectedMicrophone}
                          onChange={(e) => setSelectedMicrophone(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        >
                          {availableDevices.microphones.map((mic) => (
                            <option key={mic.deviceId} value={mic.deviceId}>
                              {mic.label || `Microphone ${mic.deviceId.slice(0, 8)}`}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {/* Stream Info */}
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Stream Title</label>
                        <input
                          type="text"
                          value={liveTitle}
                          onChange={(e) => setLiveTitle(e.target.value)}
                          placeholder="Enter your live stream title"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea
                          value={liveDescription}
                          onChange={(e) => setLiveDescription(e.target.value)}
                          placeholder="Describe what you'll be streaming about"
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        />
                      </div>
                    </div>

                    {/* Stream Key */}
                    {streamKey && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Stream Key</label>
                        <div className="flex items-center space-x-2">
                          <code className="flex-1 bg-white px-3 py-2 border border-gray-300 rounded text-sm font-mono">
                            {streamKey}
                          </code>
                          <button
                            type="button"
                            onClick={() => navigator.clipboard.writeText(streamKey)}
                            className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded text-sm transition-colors"
                          >
                            Copy
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Content Creation Form */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
            <form onSubmit={handleSubmit}>
              <div className="p-8">
                {/* Title */}
                <div className="mb-6">
                  <label htmlFor="title" className="block text-lg font-semibold text-gray-900 mb-2">
                    Title <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-text hover:border-gray-400"
                    placeholder="Enter a compelling title for your content"
                    required
                  />
                </div>

                {/* Description */}
                <div className="mb-6">
                  <label htmlFor="description" className="block text-lg font-semibold text-gray-900 mb-2">
                    Description
                  </label>
                  <textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-text hover:border-gray-400"
                    placeholder="Briefly describe what your content is about"
                  />
                </div>

                {/* Category */}
                <div className="mb-6">
                  <label htmlFor="category" className="block text-lg font-semibold text-gray-900 mb-2">
                    Category <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="category"
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-pointer hover:border-gray-400"
                    required
                  >
                    <option value="">Select a category</option>
                    {categories.map((category) => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Tags */}
                <div className="mb-6">
                  <label htmlFor="tags" className="block text-lg font-semibold text-gray-900 mb-2">
                    Tags
                  </label>
                  <input
                    type="text"
                    id="tags"
                    value={formData.tags}
                    onChange={(e) => handleInputChange('tags', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-text hover:border-gray-400"
                    placeholder="Enter tags separated by commas (e.g., technology, AI, future)"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Tags help users discover your content. Separate multiple tags with commas.
                  </p>
                </div>

                {/* Cover Image Settings */}
                {contentType !== 'live' && (
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <label className="block text-lg font-semibold text-gray-900">
                        Cover Image
                      </label>
                      <button
                        type="button"
                        onClick={() => setShowCoverSelector(!showCoverSelector)}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
                      >
                        <Settings className="w-4 h-4 mr-1" />
                        {showCoverSelector ? 'Hide Options' : 'Customize Cover'}
                      </button>
                    </div>

                    {/* Cover Preview */}
                    <div className="mb-4">
                      <div className="relative bg-gray-100 rounded-xl overflow-hidden aspect-video border-2 border-dashed border-gray-300">
                        {coverPreview ? (
                          <img
                            src={coverPreview}
                            alt="Cover preview"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full text-gray-500">
                            <div className="text-center">
                              <ImageIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
                              <p className="text-sm">No cover image selected</p>
                              <p className="text-xs opacity-75">Auto-generated cover will be used</p>
                            </div>
                          </div>
                        )}

                        {coverPreview && (
                          <div className="absolute top-2 right-2">
                            <button
                              type="button"
                              onClick={() => {
                                setCoverPreview('');
                                setCustomCover(null);
                                setCoverType('auto');
                              }}
                              className="bg-red-500 hover:bg-red-600 text-white p-1 rounded-full transition-colors"
                              title="Remove cover"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Cover Options */}
                    {showCoverSelector && (
                      <div className="bg-gray-50 rounded-xl p-4 space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {/* Auto Generate */}
                          <button
                            type="button"
                            onClick={generateAutoCover}
                            className={`p-4 rounded-lg border-2 transition-all ${
                              coverType === 'auto'
                                ? 'border-blue-500 bg-blue-50 text-blue-700'
                                : 'border-gray-200 hover:border-gray-300 text-gray-600'
                            }`}
                          >
                            <Zap className="w-6 h-6 mx-auto mb-2" />
                            <div className="font-medium">Auto Generate</div>
                            <div className="text-xs opacity-75">AI-powered cover</div>
                          </button>

                          {/* Upload Custom */}
                          <label className={`p-4 rounded-lg border-2 transition-all cursor-pointer ${
                            coverType === 'custom'
                              ? 'border-green-500 bg-green-50 text-green-700'
                              : 'border-gray-200 hover:border-gray-300 text-gray-600'
                          }`}>
                            <input
                              type="file"
                              accept="image/*"
                              onChange={handleCoverUpload}
                              className="hidden"
                            />
                            <ImageIcon className="w-6 h-6 mx-auto mb-2" />
                            <div className="font-medium">Upload Custom</div>
                            <div className="text-xs opacity-75">Your own image</div>
                          </label>

                          {/* Template */}
                          <button
                            type="button"
                            onClick={() => selectTemplateCover('/api/placeholder/600/400')}
                            className={`p-4 rounded-lg border-2 transition-all ${
                              coverType === 'template'
                                ? 'border-purple-500 bg-purple-50 text-purple-700'
                                : 'border-gray-200 hover:border-gray-300 text-gray-600'
                            }`}
                          >
                            <Palette className="w-6 h-6 mx-auto mb-2" />
                            <div className="font-medium">Use Template</div>
                            <div className="text-xs opacity-75">Pre-designed covers</div>
                          </button>
                        </div>

                        {/* Template Gallery */}
                        {coverType === 'template' && (
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-4">
                            {[1, 2, 3, 4, 5, 6, 7, 8].map((template) => (
                              <button
                                key={template}
                                type="button"
                                onClick={() => selectTemplateCover(`/api/placeholder/300/200?template=${template}`)}
                                className="aspect-video bg-gradient-to-br from-blue-400 to-purple-600 rounded-lg hover:scale-105 transition-transform"
                              >
                                <div className="w-full h-full flex items-center justify-center text-white text-xs font-medium">
                                  Template {template}
                                </div>
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {/* Content based on type */}
                {contentType === 'article' && (
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-2">
                      <label htmlFor="content" className="block text-lg font-semibold text-gray-900">
                        Content <span className="text-red-500">*</span>
                      </label>
                      <div className="text-sm text-gray-500">
                        Enhanced Editor
                      </div>
                    </div>
                    <EnhancedRichTextEditor
                      value={formData.content}
                      onChange={(content) => handleInputChange('content', content)}
                      placeholder="Write your article content here... Use the toolbar above for rich formatting options."
                      className="border-0"
                      maxHeight="600px"
                      showPreview={true}
                      enableAdvancedFeatures={true}
                    />
                  </div>
                )}

                {(contentType === 'video' || contentType === 'audio') && (
                  <div className="mb-6">
                    <label className="block text-lg font-semibold text-gray-900 mb-2">
                      Upload {contentType === 'video' ? 'Video' : 'Audio'} <span className="text-red-500">*</span>
                    </label>
                    <MediaUploader
                      type={contentType}
                      onFileSelect={(file) => handleFileUpload('mediaFile', file)}
                      onVideoInfo={contentType === 'video' ? handleVideoInfo : undefined}
                    />
                    
                    {videoInfo && (
                      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                        <h4 className="font-medium text-blue-900 mb-2">Media Information</h4>
                        <div className="grid grid-cols-2 gap-2 text-sm text-blue-800">
                          <div>Duration: {Math.floor(videoInfo.duration / 60)}:{String(Math.floor(videoInfo.duration % 60)).padStart(2, '0')}</div>
                          <div>Resolution: {videoInfo.width}×{videoInfo.height}</div>
                          <div>Size: {(videoInfo.size / (1024 * 1024)).toFixed(2)} MB</div>
                        </div>
                      </div>
                    )}
                  </div>
                )}



                {/* Video Quality Settings */}
                {contentType === 'video' && (
                  <div className="mb-6">
                    <label htmlFor="videoQuality" className="block text-base font-medium text-gray-900 mb-3">
                      Video Quality
                    </label>
                    <select
                      id="videoQuality"
                      value={formData.videoQuality}
                      onChange={(e) => setFormData(prev => ({ ...prev, videoQuality: e.target.value as typeof formData.videoQuality }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-pointer hover:border-gray-400"
                    >
                      <option value="480p">480p</option>
                      <option value="720p">720p HD</option>
                      <option value="1080p">1080p Full HD</option>
                      <option value="1440p">1440p 2K</option>
                      <option value="2160p">2160p 4K</option>
                      <option value="4320p">4320p 8K</option>
                    </select>
                  </div>
                )}

                {/* Audio Quality Settings */}
                {contentType === 'audio' && (
                  <div className="mb-6">
                    <label htmlFor="audioBitrate" className="block text-base font-medium text-gray-900 mb-3">
                      Audio Bitrate
                    </label>
                    <select
                      id="audioBitrate"
                      value={formData.audioBitrate}
                      onChange={(e) => setFormData(prev => ({ ...prev, audioBitrate: e.target.value as typeof formData.audioBitrate }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-pointer hover:border-gray-400"
                    >
                      <option value="128">128 kbps</option>
                      <option value="192">192 kbps</option>
                      <option value="256">256 kbps</option>
                      <option value="320">320 kbps</option>
                    </select>
                  </div>
                )}

                {/* AI Review Button */}
                <div className="mb-6">
                  <button
                    type="button"
                    onClick={handleAiReview}
                    className="w-full py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-all cursor-pointer"
                  >
                    🔍 Run AI Content Review
                  </button>
                </div>

                {/* AI Review Results */}
                {showAiReview && aiReviewResult && (
                  <div className={`mb-6 p-4 rounded-xl border ${
                    aiReviewResult.passed 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}>
                    <h3 className={`font-semibold text-lg mb-2 ${
                      aiReviewResult.passed ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {aiReviewResult.passed ? '✅ Content Approved' : '❌ Content Issues Detected'}
                    </h3>
                    
                    {aiReviewResult.issues.length > 0 && (
                      <div className="mb-3">
                        <h4 className="font-medium text-red-700 mb-1">Issues Found:</h4>
                        <ul className="list-disc list-inside text-red-600 space-y-1">
                          {aiReviewResult.issues.map((issue, index) => (
                            <li key={index}>{issue}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {aiReviewResult.suggestions.length > 0 && (
                      <div>
                        <h4 className="font-medium text-purple-700 mb-1">Suggestions:</h4>
                        <ul className="list-disc list-inside text-purple-600 space-y-1">
                          {aiReviewResult.suggestions.map((suggestion, index) => (
                            <li key={index}>{suggestion}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {/* Submit Button */}
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-4">
                    {/* Preview Button */}
                    <button
                      type="button"
                      onClick={() => {
                        // 预览功能
                        const previewWindow = window.open('', '_blank');
                        if (previewWindow) {
                          previewWindow.document.write(`
                            <html>
                              <head><title>Preview: ${formData.title}</title></head>
                              <body style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
                                <h1>${formData.title}</h1>
                                <p><strong>Category:</strong> ${formData.category}</p>
                                <p><strong>Tags:</strong> ${formData.tags}</p>
                                <div>${formData.content}</div>
                              </body>
                            </html>
                          `);
                        }
                      }}
                      className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium transition-all hover:bg-gray-200 flex items-center"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Preview
                    </button>

                    {/* Save Draft Button */}
                    <button
                      type="button"
                      onClick={() => {
                        localStorage.setItem('draft_content', JSON.stringify(formData));
                        alert('Draft saved successfully!');
                      }}
                      className="px-6 py-3 bg-yellow-100 text-yellow-700 rounded-xl font-medium transition-all hover:bg-yellow-200 flex items-center"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      Save Draft
                    </button>
                  </div>

                  <div className="flex items-center space-x-4">
                    {/* AI Review Button */}
                    <button
                      type="button"
                      onClick={handleAiReview}
                      className="px-6 py-3 bg-purple-100 text-purple-700 rounded-xl font-medium transition-all hover:bg-purple-200 flex items-center"
                    >
                      <Zap className="w-4 h-4 mr-2" />
                      AI Review
                    </button>

                    {/* Main Submit Button */}
                    <button
                      type="submit"
                      id="main-publish-button"
                      disabled={isSubmitting || (contentType === 'live' && !liveTitle.trim())}
                      className={`px-8 py-3 rounded-xl font-medium transition-all flex items-center ${
                        contentType === 'live'
                          ? liveStatus === 'live'
                            ? 'bg-red-600 hover:bg-red-700 text-white'
                            : 'bg-purple-600 hover:bg-purple-700 text-white'
                          : 'bg-blue-600 hover:bg-blue-700 text-white'
                      } ${
                        isSubmitting || (contentType === 'live' && !liveTitle.trim())
                          ? 'opacity-70 cursor-not-allowed'
                          : 'cursor-pointer'
                      }`}
                    >
                      {isSubmitting ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {contentType === 'live' ? 'Starting Stream...' : 'Publishing...'}
                        </span>
                      ) : (
                        <>
                          {contentType === 'live' ? (
                            liveStatus === 'live' ? (
                              <>
                                <Square className="w-4 h-4 mr-2" />
                                End Stream
                              </>
                            ) : (
                              <>
                                <Radio className="w-4 h-4 mr-2" />
                                Start Live Stream
                              </>
                            )
                          ) : (
                            <>
                              <Send className="w-4 h-4 mr-2" />
                              {isEditMode ? 'Update Content' : 'Save as Draft'}
                            </>
                          )}
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </main>

        {/* Enhanced Sidebar */}
        <aside className="w-80 space-y-6">
          {/* Live Stream Stats */}
          {(contentType === 'live' || isLiveMode) && liveStatus === 'live' && (
            <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl border border-purple-200 shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Radio className="w-5 h-5 mr-2 text-purple-600" />
                Live Stream Stats
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Current Viewers</span>
                  <span className="text-2xl font-bold text-purple-600">{liveViewers}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Stream Duration</span>
                  <span className="text-sm font-medium text-gray-900">
                    {Math.floor(Date.now() / 60000) % 60}:{String(Math.floor(Date.now() / 1000) % 60).padStart(2, '0')}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
                    <span className="text-sm font-medium text-red-600">LIVE</span>
                  </div>
                </div>
              </div>

              {/* Live Comments */}
              <div className="mt-6">
                <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Live Comments
                </h4>
                <div className="bg-white rounded-lg p-3 max-h-32 overflow-y-auto space-y-2">
                  {liveComments.length > 0 ? (
                    liveComments.slice(-5).map((comment) => (
                      <div key={comment.id} className="text-xs">
                        <span className="font-medium text-purple-600">{comment.user}:</span>
                        <span className="text-gray-700 ml-1">{comment.message}</span>
                      </div>
                    ))
                  ) : (
                    <div className="text-xs text-gray-500 text-center py-2">
                      No comments yet. Start engaging with your audience!
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Content Analytics Preview */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
              Content Insights
            </h3>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">0</div>
                  <div className="text-xs text-blue-700">Expected Views</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">0</div>
                  <div className="text-xs text-green-700">Engagement Rate</div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Content Score</span>
                  <span className="text-sm text-gray-500">85/100</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full" style={{width: '85%'}}></div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Great! Your content is optimized for engagement.
                </div>
              </div>
            </div>
          </div>

          {/* AI Creation Assistant */}
          <AICreationAssistant
            contentType={contentType}
            currentTitle={formData.title}
            currentContent={formData.content}
            onSuggestionApply={(type, suggestion) => {
              if (type === 'title') {
                setFormData(prev => ({ ...prev, title: suggestion }));
              } else if (type === 'content') {
                setFormData(prev => ({ ...prev, content: suggestion }));
              } else if (type === 'tags') {
                setFormData(prev => ({ ...prev, tags: suggestion }));
              } else if (type === 'outline') {
                setFormData(prev => ({ ...prev, content: suggestion }));
              }
            }}
            className="mb-6"
          />

          {/* Enhanced Quick Tips */}
          <QuickTips
            contentType={contentType}
            className="mb-6"
          />

          {/* Trending Topics */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">🔥 Trending Topics</h2>
            <div className="space-y-4">
              {trendingTopics.map((topic) => (
                <div 
                  key={topic.id}
                  onClick={() => handleTrendingTopicClick(topic)}
                  className="p-3 border border-gray-200 rounded-xl hover:bg-gray-100 cursor-pointer transition-colors group"
                >
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-gray-900 group-hover:text-blue-600">{topic.title}</h3>
                    <span className="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                      {topic.type}
                    </span>
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-gray-500">{topic.category}</span>
                    <div className="flex items-center text-sm">
                      <span className="text-gray-500 mr-2">{topic.views} views</span>
                      <span className="text-green-600 font-medium">{topic.trend}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </aside>
      </div>


    </div>
  );
}

export default function CreatePage() {
  return (
    <AuthGuard 
      requireAuth={true} 
      redirectTo="/auth/login"
    >
      <CreateContent />
    </AuthGuard>
  );
}