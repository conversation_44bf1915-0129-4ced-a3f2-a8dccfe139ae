# Newzora 项目上线前一键清理脚本
# 版本: 1.0.0
# 作者: Newzora Team
# 日期: 2025-01-04

param(
    [switch]$DryRun,
    [switch]$Verbose,
    [switch]$SkipConfirmation
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    switch ($Color) {
        "Red" { Write-Host $Message -ForegroundColor Red }
        "Green" { Write-Host $Message -ForegroundColor Green }
        "Yellow" { Write-Host $Message -ForegroundColor Yellow }
        "Blue" { Write-Host $Message -ForegroundColor Blue }
        "Cyan" { Write-Host $Message -ForegroundColor Cyan }
        "Magenta" { Write-Host $Message -ForegroundColor Magenta }
        default { Write-Host $Message }
    }
}

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { Write-ColorOutput $logMessage "Red" }
        "WARN" { Write-ColorOutput $logMessage "Yellow" }
        "SUCCESS" { Write-ColorOutput $logMessage "Green" }
        "INFO" { Write-ColorOutput $logMessage "Cyan" }
        default { Write-ColorOutput $logMessage }
    }
    
    if ($Verbose) {
        Add-Content -Path "cleanup-log.txt" -Value $logMessage
    }
}

# 检查目录是否存在
function Test-Directory {
    param([string]$Path)
    return Test-Path -Path $Path -PathType Container
}

# 安全删除函数
function Remove-SafelyWithConfirmation {
    param(
        [string]$Path,
        [string]$Description,
        [switch]$Force
    )
    
    if (-not (Test-Path $Path)) {
        Write-Log "路径不存在，跳过: $Path" "WARN"
        return
    }
    
    if ($DryRun) {
        Write-Log "[DRY RUN] 将删除: $Description - $Path" "INFO"
        return
    }
    
    if (-not $SkipConfirmation -and -not $Force) {
        $confirmation = Read-Host "确认删除 $Description ($Path)? (y/N)"
        if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
            Write-Log "跳过删除: $Description" "INFO"
            return
        }
    }
    
    try {
        if (Test-Path $Path -PathType Container) {
            Remove-Item -Path $Path -Recurse -Force
        }
        else {
            Remove-Item -Path $Path -Force
        }
        Write-Log "成功删除: $Description" "SUCCESS"
    }
    catch {
        Write-Log "删除失败: $Description - $($_.Exception.Message)" "ERROR"
    }
}

# 清理统计
$script:CleanupStats = @{
    FilesDeleted       = 0
    DirectoriesDeleted = 0
    SpaceSaved         = 0
    ErrorsEncountered  = 0
}

# 计算文件大小
function Get-DirectorySize {
    param([string]$Path)
    if (Test-Path $Path) {
        return (Get-ChildItem -Path $Path -Recurse -File | Measure-Object -Property Length -Sum).Sum
    }
    return 0
}

Write-ColorOutput @"
╔══════════════════════════════════════════════════════════════╗
║                    Newzora 上线前清理脚本                    ║
║                        Version 1.0.0                        ║
╚══════════════════════════════════════════════════════════════╝
"@ "Cyan"

Write-Log "开始执行 Newzora 项目上线前清理..." "INFO"

if ($DryRun) {
    Write-Log "运行模式: DRY RUN (仅显示将要删除的内容)" "WARN"
}

# 1. 清理开发和测试文件
Write-ColorOutput "`n🧪 清理开发和测试文件..." "Yellow"

$testPaths = @(
    @{ Path = "Frontend/__tests__"; Desc = "前端测试目录" }
    @{ Path = "Frontend/src/app/test-fixes"; Desc = "测试修复页面" }
    @{ Path = "Frontend/src/components/__tests__"; Desc = "组件测试文件" }
    @{ Path = "Frontend/src/services/__tests__"; Desc = "服务测试文件" }
    @{ Path = "Frontend/src/utils/__tests__"; Desc = "工具函数测试文件" }
    @{ Path = "NewzoraAdmin/Frontend/__tests__"; Desc = "后台测试目录" }
    @{ Path = "NewzoraAdmin/Frontend/src/components/__tests__"; Desc = "后台组件测试" }
    @{ Path = "Backend/__tests__"; Desc = "后端测试目录" }
    @{ Path = "Backend/tests"; Desc = "后端测试文件" }
    @{ Path = "tests"; Desc = "根目录测试文件" }
)

foreach ($item in $testPaths) {
    $sizeBefore = Get-DirectorySize $item.Path
    Remove-SafelyWithConfirmation -Path $item.Path -Description $item.Desc
    if (-not (Test-Path $item.Path)) {
        $script:CleanupStats.DirectoriesDeleted++
        $script:CleanupStats.SpaceSaved += $sizeBefore
    }
}

# 2. 清理临时和缓存文件
Write-ColorOutput "`n🗂️ 清理临时和缓存文件..." "Yellow"

$tempPaths = @(
    @{ Path = "Frontend/.next"; Desc = "Next.js 构建缓存" }
    @{ Path = "Frontend/node_modules/.cache"; Desc = "Node.js 缓存" }
    @{ Path = "Frontend/tsconfig.tsbuildinfo"; Desc = "TypeScript 构建信息" }
    @{ Path = "NewzoraAdmin/Frontend/.next"; Desc = "后台 Next.js 缓存" }
    @{ Path = "NewzoraAdmin/Frontend/node_modules/.cache"; Desc = "后台 Node.js 缓存" }
    @{ Path = "NewzoraAdmin/Frontend/tsconfig.tsbuildinfo"; Desc = "后台 TypeScript 构建信息" }
    @{ Path = "Backend/node_modules/.cache"; Desc = "后端 Node.js 缓存" }
    @{ Path = "Backend/logs/*.log"; Desc = "后端日志文件" }
    @{ Path = "*.log"; Desc = "根目录日志文件" }
    @{ Path = "npm-debug.log*"; Desc = "NPM 调试日志" }
    @{ Path = "yarn-debug.log*"; Desc = "Yarn 调试日志" }
    @{ Path = "yarn-error.log*"; Desc = "Yarn 错误日志" }
)

foreach ($item in $tempPaths) {
    if ($item.Path -like "*`**") {
        # 处理通配符路径
        $files = Get-ChildItem -Path $item.Path -ErrorAction SilentlyContinue
        foreach ($file in $files) {
            $sizeBefore = $file.Length
            Remove-SafelyWithConfirmation -Path $file.FullName -Description "$($item.Desc) - $($file.Name)"
            if (-not (Test-Path $file.FullName)) {
                $script:CleanupStats.FilesDeleted++
                $script:CleanupStats.SpaceSaved += $sizeBefore
            }
        }
    }
    else {
        $sizeBefore = Get-DirectorySize $item.Path
        Remove-SafelyWithConfirmation -Path $item.Path -Description $item.Desc
        if (-not (Test-Path $item.Path)) {
            if (Test-Path $item.Path -PathType Container) {
                $script:CleanupStats.DirectoriesDeleted++
            }
            else {
                $script:CleanupStats.FilesDeleted++
            }
            $script:CleanupStats.SpaceSaved += $sizeBefore
        }
    }
}

# 3. 清理开发工具和配置文件
Write-ColorOutput "`n🔧 清理开发工具文件..." "Yellow"

$devFiles = @(
    @{ Path = "Frontend/jest.config.js"; Desc = "Jest 配置文件" }
    @{ Path = "Frontend/jest.setup.js"; Desc = "Jest 设置文件" }
    @{ Path = "NewzoraAdmin/Frontend/jest.config.js"; Desc = "后台 Jest 配置" }
    @{ Path = "NewzoraAdmin/Frontend/jest.setup.js"; Desc = "后台 Jest 设置" }
    @{ Path = ".vscode"; Desc = "VS Code 配置目录" }
    @{ Path = ".idea"; Desc = "IntelliJ IDEA 配置" }
    @{ Path = "*.swp"; Desc = "Vim 交换文件" }
    @{ Path = "*.swo"; Desc = "Vim 交换文件" }
    @{ Path = ".DS_Store"; Desc = "macOS 系统文件" }
    @{ Path = "Thumbs.db"; Desc = "Windows 缩略图缓存" }
)

foreach ($item in $devFiles) {
    if ($item.Path -like "*`**") {
        $files = Get-ChildItem -Path $item.Path -ErrorAction SilentlyContinue
        foreach ($file in $files) {
            Remove-SafelyWithConfirmation -Path $file.FullName -Description "$($item.Desc) - $($file.Name)"
        }
    }
    else {
        Remove-SafelyWithConfirmation -Path $item.Path -Description $item.Desc
    }
}

# 4. 清理重复和备份文件
Write-ColorOutput "`n📄 清理重复和备份文件..." "Yellow"

$duplicatePatterns = @(
    "*.bak"
    "*.backup"
    "*.old"
    "*.orig"
    "*.tmp"
    "*.temp"
    "*~"
    "*.new"
    "*.copy"
    "*-copy.*"
    "*-backup.*"
)

foreach ($pattern in $duplicatePatterns) {
    $files = Get-ChildItem -Path . -Recurse -Name $pattern -ErrorAction SilentlyContinue
    foreach ($file in $files) {
        Remove-SafelyWithConfirmation -Path $file -Description "重复/备份文件: $file"
    }
}

# 5. 清理调试和开发代码
Write-ColorOutput "`n🐛 扫描并清理调试代码..." "Yellow"

$debugPatterns = @(
    "console.log"
    "console.debug"
    "console.warn"
    "console.error"
    "debugger;"
    "TODO:"
    "FIXME:"
    "HACK:"
    "XXX:"
)

function Scan-DebugCode {
    param([string]$Directory)

    $jsFiles = Get-ChildItem -Path $Directory -Recurse -Include "*.js", "*.jsx", "*.ts", "*.tsx" -ErrorAction SilentlyContinue
    $debugFound = @()

    foreach ($file in $jsFiles) {
        $content = Get-Content -Path $file.FullName -ErrorAction SilentlyContinue
        $lineNumber = 0

        foreach ($line in $content) {
            $lineNumber++
            foreach ($pattern in $debugPatterns) {
                if ($line -match $pattern) {
                    $debugFound += @{
                        File    = $file.FullName
                        Line    = $lineNumber
                        Content = $line.Trim()
                        Pattern = $pattern
                    }
                }
            }
        }
    }

    return $debugFound
}

$frontendDebug = Scan-DebugCode "Frontend/src"
$adminDebug = Scan-DebugCode "NewzoraAdmin/Frontend/src"
$backendDebug = Scan-DebugCode "Backend"

$allDebug = $frontendDebug + $adminDebug + $backendDebug

if ($allDebug.Count -gt 0) {
    Write-Log "发现 $($allDebug.Count) 个调试代码片段:" "WARN"
    foreach ($debug in $allDebug) {
        Write-Log "  $($debug.File):$($debug.Line) - $($debug.Content)" "WARN"
    }

    if (-not $DryRun) {
        $cleanDebug = Read-Host "是否自动清理这些调试代码? (y/N)"
        if ($cleanDebug -eq 'y' -or $cleanDebug -eq 'Y') {
            # 这里可以添加自动清理逻辑
            Write-Log "调试代码清理功能需要手动实现" "INFO"
        }
    }
}
else {
    Write-Log "未发现调试代码，代码质量良好!" "SUCCESS"
}

# 6. 清理空目录
Write-ColorOutput "`n📁 清理空目录..." "Yellow"

function Remove-EmptyDirectories {
    param([string]$Path)

    $directories = Get-ChildItem -Path $Path -Recurse -Directory -ErrorAction SilentlyContinue | Sort-Object FullName -Descending

    foreach ($dir in $directories) {
        $items = Get-ChildItem -Path $dir.FullName -ErrorAction SilentlyContinue
        if ($items.Count -eq 0) {
            Remove-SafelyWithConfirmation -Path $dir.FullName -Description "空目录: $($dir.Name)" -Force
            if (-not (Test-Path $dir.FullName)) {
                $script:CleanupStats.DirectoriesDeleted++
            }
        }
    }
}

Remove-EmptyDirectories "Frontend"
Remove-EmptyDirectories "NewzoraAdmin"
Remove-EmptyDirectories "Backend"

# 7. 优化 package.json
Write-ColorOutput "`n📦 检查 package.json 优化..." "Yellow"

$packageFiles = @(
    "Frontend/package.json"
    "NewzoraAdmin/Frontend/package.json"
    "Backend/package.json"
)

foreach ($packageFile in $packageFiles) {
    if (Test-Path $packageFile) {
        $package = Get-Content $packageFile | ConvertFrom-Json

        # 检查是否有测试相关的依赖
        $testDeps = @()
        if ($package.devDependencies) {
            foreach ($dep in $package.devDependencies.PSObject.Properties) {
                if ($dep.Name -match "test|jest|@testing-library") {
                    $testDeps += $dep.Name
                }
            }
        }

        if ($testDeps.Count -gt 0) {
            Write-Log "在 $packageFile 中发现测试依赖: $($testDeps -join ', ')" "WARN"
            if (-not $DryRun) {
                $removeTestDeps = Read-Host "是否移除这些测试依赖? (y/N)"
                if ($removeTestDeps -eq 'y' -or $removeTestDeps -eq 'Y') {
                    Write-Log "建议手动编辑 package.json 移除测试依赖" "INFO"
                }
            }
        }
    }
}

# 8. 生成清理报告
Write-ColorOutput "`n📊 生成清理报告..." "Yellow"

$reportContent = @"
# Newzora 项目清理报告
生成时间: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

## 清理统计
- 删除文件数: $($script:CleanupStats.FilesDeleted)
- 删除目录数: $($script:CleanupStats.DirectoriesDeleted)
- 节省空间: $([math]::Round($script:CleanupStats.SpaceSaved / 1MB, 2)) MB
- 遇到错误: $($script:CleanupStats.ErrorsEncountered)

## 清理项目
✅ 开发和测试文件
✅ 临时和缓存文件
✅ 开发工具配置
✅ 重复和备份文件
✅ 调试代码扫描
✅ 空目录清理
✅ package.json 优化检查

## 建议的后续操作
1. 运行 npm run build 确保项目可以正常构建
2. 检查所有环境变量配置
3. 更新生产环境配置
4. 进行最终的功能测试
5. 准备部署脚本

## 注意事项
- 请确保备份重要数据
- 在生产环境部署前进行充分测试
- 监控应用性能和错误日志
"@

if (-not $DryRun) {
    $reportContent | Out-File -FilePath "cleanup-report.md" -Encoding UTF8
    Write-Log "清理报告已保存到: cleanup-report.md" "SUCCESS"
}

# 最终总结
Write-ColorOutput @"

╔══════════════════════════════════════════════════════════════╗
║                        清理完成!                            ║
╠══════════════════════════════════════════════════════════════╣
║ 删除文件: $($script:CleanupStats.FilesDeleted.ToString().PadLeft(10))                                    ║
║ 删除目录: $($script:CleanupStats.DirectoriesDeleted.ToString().PadLeft(10))                                    ║
║ 节省空间: $([math]::Round($script:CleanupStats.SpaceSaved / 1MB, 2).ToString().PadLeft(8)) MB                                ║
╚══════════════════════════════════════════════════════════════╝
"@ "Green"

Write-Log "Newzora 项目清理完成! 项目已准备好上线部署。" "SUCCESS"

if ($DryRun) {
    Write-ColorOutput "`n💡 这是 DRY RUN 模式，没有实际删除任何文件。" "Yellow"
    Write-ColorOutput "   要执行实际清理，请运行: .\cleanup-production.ps1" "Yellow"
}

Write-ColorOutput "`n🚀 下一步: 运行构建命令验证项目完整性" "Cyan"
Write-ColorOutput "   Frontend: cd Frontend && npm run build" "Cyan"
Write-ColorOutput "   Admin: cd NewzoraAdmin/Frontend && npm run build" "Cyan"
Write-ColorOutput "   Backend: cd Backend && npm start" "Cyan"
