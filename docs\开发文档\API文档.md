# Newzora API 文档

## 基础信息

- **Base URL**: `http://localhost:5000/api` (开发环境)
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON

## 认证 API

### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "username",
  "display_name": "Display Name"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "username": "username",
      "display_name": "Display Name"
    },
    "token": "jwt_token"
  }
}
```

### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 获取当前用户
```http
GET /api/auth/me
Authorization: Bearer {token}
```

### 更新用户资料
```http
PUT /api/auth/profile
Authorization: Bearer {token}
Content-Type: application/json

{
  "display_name": "New Display Name",
  "bio": "User bio",
  "avatar_url": "https://example.com/avatar.jpg"
}
```

### 修改密码
```http
PUT /api/auth/change-password
Authorization: Bearer {token}
Content-Type: application/json

{
  "current_password": "old_password",
  "new_password": "new_password"
}
```

## 文章 API

### 获取文章列表
```http
GET /api/articles?page=1&limit=10&category=tech&sort=latest
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10, 最大: 50)
- `category`: 分类筛选
- `sort`: 排序方式 (latest, popular, oldest)
- `search`: 搜索关键词

### 获取单篇文章
```http
GET /api/articles/{id}
```

### 创建文章
```http
POST /api/articles
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "文章标题",
  "content": "文章内容",
  "excerpt": "文章摘要",
  "category_id": 1,
  "tags": ["tag1", "tag2"],
  "featured_image": "https://example.com/image.jpg",
  "status": "published"
}
```

### 更新文章
```http
PUT /api/articles/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "更新的标题",
  "content": "更新的内容"
}
```

### 删除文章
```http
DELETE /api/articles/{id}
Authorization: Bearer {token}
```

## 评论 API

### 获取文章评论
```http
GET /api/comments/{article_id}?page=1&limit=20
```

### 创建评论
```http
POST /api/comments
Authorization: Bearer {token}
Content-Type: application/json

{
  "article_id": "article_uuid",
  "content": "评论内容",
  "parent_id": "parent_comment_uuid" // 可选，用于回复
}
```

### 更新评论
```http
PUT /api/comments/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "更新的评论内容"
}
```

### 删除评论
```http
DELETE /api/comments/{id}
Authorization: Bearer {token}
```

## 用户 API

### 获取用户列表
```http
GET /api/users?page=1&limit=10&search=username
```

### 获取用户资料
```http
GET /api/users/{id}
```

### 关注用户
```http
POST /api/users/{id}/follow
Authorization: Bearer {token}
```

### 取消关注
```http
DELETE /api/users/{id}/follow
Authorization: Bearer {token}
```

### 获取用户关注列表
```http
GET /api/users/{id}/following?page=1&limit=20
```

### 获取用户粉丝列表
```http
GET /api/users/{id}/followers?page=1&limit=20
```

## 分类和标签 API

### 获取分类列表
```http
GET /api/categories
```

### 获取标签列表
```http
GET /api/tags?search=keyword&limit=20
```

## 媒体 API

### 上传文件
```http
POST /api/media/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
  "file": File,
  "type": "image" // image, video, document
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "url": "https://example.com/uploads/file.jpg",
    "filename": "file.jpg",
    "size": 1024,
    "type": "image/jpeg"
  }
}
```

## 错误响应

所有错误响应都遵循以下格式：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {} // 可选的详细信息
  }
}
```

### 常见错误码

- `UNAUTHORIZED`: 未授权访问
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `VALIDATION_ERROR`: 数据验证失败
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `INTERNAL_ERROR`: 服务器内部错误

## 状态码

- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 数据验证失败
- `429`: 请求频率超限
- `500`: 服务器内部错误

## 分页

所有列表接口都支持分页，响应格式：

```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "pages": 10,
      "has_next": true,
      "has_prev": false
    }
  }
}
```