# 📧 Gmail SMTP 配置指南

## 🎯 目标
为Newzora项目配置真实的Gmail SMTP服务，实现邮箱验证和密码重置功能。

## 📋 配置步骤

### 1. 准备Gmail账户

#### 1.1 启用两步验证
1. 登录您的Gmail账户
2. 访问 [Google账户安全设置](https://myaccount.google.com/security)
3. 在"登录Google"部分，启用"两步验证"
4. 按照提示完成两步验证设置

#### 1.2 生成应用专用密码
1. 在Google账户安全页面，找到"应用专用密码"
2. 点击"应用专用密码"
3. 选择"邮件"和"其他(自定义名称)"
4. 输入"Newzora"作为应用名称
5. 点击"生成"
6. **重要**: 复制生成的16位密码，这将是您的`EMAIL_PASS`

### 2. 更新环境配置

#### 2.1 修改Backend/.env文件
```env
# Email Configuration (using Gmail SMTP)
EMAIL_PROVIDER=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_REQUIRE_TLS=true
# 替换为您的真实Gmail地址
EMAIL_USER=<EMAIL>
# 替换为步骤1.2中生成的应用专用密码
EMAIL_PASS=your-16-digit-app-password
EMAIL_FROM=Newzora <<EMAIL>>
```

#### 2.2 示例配置
```env
# 示例 (请使用您自己的信息)
EMAIL_USER=<EMAIL>
EMAIL_PASS=abcd efgh ijkl mnop
EMAIL_FROM=Newzora <<EMAIL>>
```

### 3. 测试邮件配置

#### 3.1 使用内置测试端点
```bash
# 启动后端服务
cd Backend
npm run dev

# 测试邮件发送 (使用Postman或curl)
curl -X POST http://localhost:5000/api/auth/test-email \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "subject": "Newzora邮件测试",
    "message": "这是一封测试邮件"
  }'
```

#### 3.2 预期响应
```json
{
  "success": true,
  "message": "Test email sent successfully"
}
```

### 4. 验证功能测试

#### 4.1 注册流程测试
1. 访问 `http://localhost:3000/auth/register`
2. 使用真实邮箱地址注册
3. 检查邮箱是否收到验证邮件
4. 点击验证链接完成注册

#### 4.2 密码重置测试
1. 访问 `http://localhost:3000/auth/forgot-password`
2. 输入注册时使用的邮箱
3. 检查邮箱是否收到重置邮件
4. 点击重置链接设置新密码

## 🚨 常见问题解决

### 问题1: "Invalid login credentials"
**原因**: 使用了普通密码而不是应用专用密码
**解决**: 确保使用步骤1.2生成的16位应用专用密码

### 问题2: "Connection timeout"
**原因**: 网络或防火墙问题
**解决**: 
- 检查网络连接
- 确认端口587未被阻止
- 尝试使用端口465 (SSL)

### 问题3: "Authentication failed"
**原因**: 邮箱地址或密码错误
**解决**:
- 确认邮箱地址正确
- 重新生成应用专用密码
- 检查两步验证是否已启用

### 问题4: 邮件进入垃圾箱
**原因**: Gmail安全策略
**解决**:
- 在Gmail中将发送地址添加到联系人
- 检查垃圾邮件文件夹
- 考虑使用专业邮件服务(如SendGrid)

## 🔒 安全注意事项

### 1. 密码安全
- ✅ 使用应用专用密码，不是账户密码
- ✅ 不要在代码中硬编码密码
- ✅ 使用环境变量存储敏感信息
- ✅ 定期更换应用专用密码

### 2. 邮箱安全
- ✅ 使用专门的邮箱账户用于应用发送
- ✅ 启用两步验证
- ✅ 监控异常登录活动
- ✅ 定期检查应用专用密码使用情况

### 3. 生产环境建议
- 🔄 考虑使用专业邮件服务(SendGrid, AWS SES)
- 🔄 配置DKIM和SPF记录
- 🔄 使用专门的发送域名
- 🔄 实施邮件发送限制和监控

## 📊 配置验证清单

- [ ] Gmail账户已启用两步验证
- [ ] 已生成应用专用密码
- [ ] Backend/.env文件已更新
- [ ] 邮件测试端点返回成功
- [ ] 注册邮件验证正常工作
- [ ] 密码重置邮件正常工作
- [ ] 邮件未进入垃圾箱
- [ ] 邮件模板显示正常

## 🎉 完成后的功能

配置完成后，您将拥有：

1. **用户注册邮箱验证** - 新用户注册后收到验证邮件
2. **密码重置功能** - 用户可以通过邮箱重置密码
3. **欢迎邮件** - 用户验证邮箱后收到欢迎邮件
4. **通知邮件** - 系统通知和提醒邮件
5. **每日摘要** - 定期发送内容摘要邮件

## 📞 技术支持

如果在配置过程中遇到问题：

1. 检查控制台日志中的错误信息
2. 验证所有环境变量是否正确设置
3. 确认Gmail账户设置正确
4. 参考本文档的常见问题部分

---

**配置完成后，请删除或注释掉测试用的邮箱配置，确保生产环境安全。**