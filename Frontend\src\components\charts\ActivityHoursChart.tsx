'use client';

import React, { useMemo, useState, useEffect } from 'react';
import { Clock, Activity, BarChart3 } from 'lucide-react';

interface ActivityHourData {
  hour: number;
  activity: number;
  users: number;
  engagement: number;
}

interface ActivityHoursChartProps {
  data?: ActivityHourData[];
  className?: string;
  dateRange?: '7d' | '30d' | '90d';
}

/**
 * 活动时间分析图表组件
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const ActivityHoursChart: React.FC<ActivityHoursChartProps> = ({ data, className = '', dateRange = '30d' }) => {
  const [chartData, setChartData] = useState<ActivityHourData[]>([]);

  // 生成模拟数据（根据日期范围调整）
  const generateMockData = (days: number): ActivityHourData[] => {
    const hours = Array.from({ length: 24 }, (_, i) => {
      // 模拟真实的用户活动模式，根据天数调整基础值
      let baseActivity = 20 + (days / 10); // 天数越多，基础活动越高

      // 早晨高峰 (7-9)
      if (i >= 7 && i <= 9) baseActivity += 30 + Math.random() * 20;
      // 午餐时间 (12-13)
      if (i >= 12 && i <= 13) baseActivity += 25 + Math.random() * 15;
      // 晚间高峰 (18-22)
      if (i >= 18 && i <= 22) baseActivity += 40 + Math.random() * 30;
      // 深夜低谷 (0-6)
      if (i >= 0 && i <= 6) baseActivity = 5 + Math.random() * 10;

      const activity = Math.floor(baseActivity + Math.random() * 10);
      const users = Math.floor(activity * (0.8 + Math.random() * 0.4));
      const engagement = Math.floor(50 + Math.random() * 40);

      return {
        hour: i,
        activity,
        users,
        engagement
      };
    });
    return hours;
  };

  // 当日期范围改变时更新数据
  useEffect(() => {
    const days = dateRange === '7d' ? 7 : dateRange === '30d' ? 30 : 90;
    const mockData = data || generateMockData(days);
    setChartData(mockData);
  }, [dateRange, data]);

  // 将24小时数据分组为时间段用于饼图显示
  const timeSegments = useMemo(() => {
    if (!chartData || chartData.length === 0) return [];

    const segments = [
      { name: '深夜', hours: [0, 1, 2, 3, 4, 5], color: '#1e40af', activity: 0 },
      { name: '清晨', hours: [6, 7, 8, 9, 10, 11], color: '#059669', activity: 0 },
      { name: '下午', hours: [12, 13, 14, 15, 16, 17], color: '#d97706', activity: 0 },
      { name: '晚上', hours: [18, 19, 20, 21, 22, 23], color: '#dc2626', activity: 0 }
    ];

    // 计算每个时间段的活动总量
    segments.forEach(segment => {
      segment.activity = segment.hours.reduce((sum, hour) => {
        const hourData = chartData.find(d => d.hour === hour);
        return sum + (hourData?.activity || 0);
      }, 0);
    });

    const totalActivity = segments.reduce((sum, s) => sum + s.activity, 0);

    // 计算百分比和角度
    let currentAngle = 0;
    return segments.map(segment => {
      const percentage = totalActivity > 0 ? (segment.activity / totalActivity) * 100 : 0;
      const angle = (percentage / 100) * 360;
      const startAngle = currentAngle;
      currentAngle += angle;

      return {
        ...segment,
        percentage,
        angle,
        startAngle,
        endAngle: currentAngle
      };
    });
  }, [chartData]);

  // 找出最活跃的时间段
  const mostActiveSegment = useMemo(() => {
    if (!timeSegments.length) return null;
    return timeSegments.reduce((max, current) =>
      current.activity > max.activity ? current : max
    );
  }, [timeSegments]);

  if (!chartData || chartData.length === 0) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Clock className="w-5 h-5 mr-2 text-blue-600" />
          活动时间分析
        </h4>
        <div className="h-48 bg-gray-50 rounded-lg flex items-center justify-center">
          <div className="text-center text-gray-500">
            <Clock className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>暂无数据</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <Clock className="w-5 h-5 mr-2 text-blue-600" />
        活动时间分析
      </h4>

      <div className="flex items-center justify-between">
        {/* 饼图 */}
        <div className="flex-1 flex justify-center">
          <div className="relative w-48 h-48">
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
              {timeSegments.map((segment, index) => {
                const radius = 40;
                const centerX = 50;
                const centerY = 50;

                const startAngleRad = (segment.startAngle * Math.PI) / 180;
                const endAngleRad = (segment.endAngle * Math.PI) / 180;

                const x1 = centerX + radius * Math.cos(startAngleRad);
                const y1 = centerY + radius * Math.sin(startAngleRad);
                const x2 = centerX + radius * Math.cos(endAngleRad);
                const y2 = centerY + radius * Math.sin(endAngleRad);

                const largeArcFlag = segment.angle > 180 ? 1 : 0;

                const pathData = [
                  `M ${centerX} ${centerY}`,
                  `L ${x1} ${y1}`,
                  `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                  'Z'
                ].join(' ');

                return (
                  <path
                    key={index}
                    d={pathData}
                    fill={segment.color}
                    className="hover:opacity-80 transition-opacity cursor-pointer"
                    title={`${segment.name}: ${segment.percentage.toFixed(1)}%`}
                  />
                );
              })}

              {/* 中心圆 */}
              <circle
                cx="50"
                cy="50"
                r="15"
                fill="white"
                stroke="#e5e7eb"
                strokeWidth="2"
              />
            </svg>

            {/* 中心文字 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">
                  {mostActiveSegment?.name || 'N/A'}
                </div>
                <div className="text-xs text-gray-500">最活跃</div>
              </div>
            </div>
          </div>
        </div>

        {/* 图例和统计 */}
        <div className="flex-1 pl-6">
          <div className="space-y-3">
            {timeSegments.map((segment, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: segment.color }}
                  ></div>
                  <span className="text-sm text-gray-700">{segment.name}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {segment.percentage.toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-500">
                    {segment.activity} 活动
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivityHoursChart;
