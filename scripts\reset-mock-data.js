#!/usr/bin/env node

/**
 * Newzora Mock Data Reset Script
 * 重置前端主站所有mock数据的互动指标为零
 * 保留媒体内容和基本信息，仅清理用户互动数据
 */

const fs = require('fs');
const path = require('path');

// 配置
const config = {
  frontendPath: path.join(__dirname, '../Frontend/src'),
  backupPath: path.join(__dirname, '../backups/mock-data-backup'),
  dryRun: process.argv.includes('--dry-run'),
  verbose: process.argv.includes('--verbose'),
  skipBackup: process.argv.includes('--skip-backup')
};

// 日志函数
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const colors = {
    info: '\x1b[36m',    // cyan
    success: '\x1b[32m', // green
    warning: '\x1b[33m', // yellow
    error: '\x1b[31m',   // red
    reset: '\x1b[0m'     // reset
  };
  
  console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
}

// 创建备份
function createBackup() {
  if (config.skipBackup) {
    log('跳过备份创建', 'warning');
    return;
  }

  log('创建数据备份...', 'info');
  
  if (!fs.existsSync(config.backupPath)) {
    fs.mkdirSync(config.backupPath, { recursive: true });
  }

  const filesToBackup = [
    'data/mockWorks.ts',
    'data/mockArticles.ts',
    'services/followService.ts',
    'components/AdBanner.tsx'
  ];

  filesToBackup.forEach(file => {
    const sourcePath = path.join(config.frontendPath, file);
    const backupPath = path.join(config.backupPath, file);
    
    if (fs.existsSync(sourcePath)) {
      const backupDir = path.dirname(backupPath);
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }
      
      fs.copyFileSync(sourcePath, backupPath);
      log(`备份文件: ${file}`, 'success');
    }
  });
}

// 重置互动指标和模拟数据的函数
function resetInteractionMetrics(content) {
  // 重置数字类型的互动指标
  const patterns = [
    { pattern: /views:\s*\d+/g, replacement: 'views: 0' },
    { pattern: /likes:\s*\d+/g, replacement: 'likes: 0' },
    { pattern: /comments:\s*\d+/g, replacement: 'comments: 0' },
    { pattern: /shares:\s*\d+/g, replacement: 'shares: 0' },
    { pattern: /bookmarks:\s*\d+/g, replacement: 'bookmarks: 0' },
    { pattern: /followers:\s*\d+/g, replacement: 'followers: 0' },
    { pattern: /following:\s*\d+/g, replacement: 'following: 0' },
    { pattern: /clicks:\s*\d+/g, replacement: 'clicks: 0' },
    { pattern: /impressions:\s*\d+/g, replacement: 'impressions: 0' },
    { pattern: /ctr:\s*[\d.]+/g, replacement: 'ctr: 0' },
    { pattern: /revenue:\s*[\d.]+/g, replacement: 'revenue: 0' },
    { pattern: /spent:\s*[\d.]+/g, replacement: 'spent: 0' },
    { pattern: /earnings:\s*[\d.]+/g, replacement: 'earnings: 0' },
    { pattern: /balance:\s*[\d.]+/g, replacement: 'balance: 0' },
    { pattern: /totalRevenue:\s*[\d.]+/g, replacement: 'totalRevenue: 0' },
    { pattern: /monthlyRevenue:\s*[\d.]+/g, replacement: 'monthlyRevenue: 0' },
    { pattern: /dailyRevenue:\s*[\d.]+/g, replacement: 'dailyRevenue: 0' },
    { pattern: /totalViews:\s*\d+/g, replacement: 'totalViews: 0' },
    { pattern: /totalLikes:\s*\d+/g, replacement: 'totalLikes: 0' },
    { pattern: /totalComments:\s*\d+/g, replacement: 'totalComments: 0' },
    { pattern: /totalShares:\s*\d+/g, replacement: 'totalShares: 0' },
    { pattern: /subscriberCount:\s*\d+/g, replacement: 'subscriberCount: 0' },
    { pattern: /viewCount:\s*\d+/g, replacement: 'viewCount: 0' },
    { pattern: /likeCount:\s*\d+/g, replacement: 'likeCount: 0' },
    { pattern: /commentCount:\s*\d+/g, replacement: 'commentCount: 0' },
    { pattern: /shareCount:\s*\d+/g, replacement: 'shareCount: 0' }
  ];

  // 清空评论数组
  const commentArrayPatterns = [
    { pattern: /comments:\s*\[[\s\S]*?\]/g, replacement: 'comments: []' },
    { pattern: /replies:\s*\[[\s\S]*?\]/g, replacement: 'replies: []' },
    { pattern: /mockComments:\s*\[[\s\S]*?\]/g, replacement: 'mockComments: []' },
    { pattern: /commentList:\s*\[[\s\S]*?\]/g, replacement: 'commentList: []' }
  ];

  let updatedContent = content;
  let changesCount = 0;

  // 处理数字指标
  patterns.forEach(({ pattern, replacement }) => {
    const matches = updatedContent.match(pattern);
    if (matches) {
      changesCount += matches.length;
      updatedContent = updatedContent.replace(pattern, replacement);
    }
  });

  // 处理评论数组
  commentArrayPatterns.forEach(({ pattern, replacement }) => {
    const matches = updatedContent.match(pattern);
    if (matches) {
      changesCount += matches.length;
      updatedContent = updatedContent.replace(pattern, replacement);
    }
  });

  return { content: updatedContent, changes: changesCount };
}

// 处理单个文件
function processFile(filePath) {
  const relativePath = path.relative(config.frontendPath, filePath);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { content: updatedContent, changes } = resetInteractionMetrics(content);
    
    if (changes > 0) {
      if (config.dryRun) {
        log(`[DRY RUN] 将重置 ${relativePath} 中的 ${changes} 个互动指标`, 'warning');
      } else {
        fs.writeFileSync(filePath, updatedContent, 'utf8');
        log(`重置 ${relativePath} 中的 ${changes} 个互动指标`, 'success');
      }
      return changes;
    } else {
      if (config.verbose) {
        log(`${relativePath} 无需更改`, 'info');
      }
      return 0;
    }
  } catch (error) {
    log(`处理文件 ${relativePath} 时出错: ${error.message}`, 'error');
    return 0;
  }
}

// 查找需要处理的文件
function findFilesToProcess() {
  const files = [];

  // 直接指定需要处理的文件
  const targetFiles = [
    'data/mockWorks.ts',
    'data/mockArticles.ts',
    'services/followService.ts',
    'components/AdBanner.tsx',
    'components/CommentSection.tsx',
    'components/RevenueDashboard.tsx',
    'components/AnalyticsDashboard.tsx',
    'components/InteractionStats.tsx'
  ];

  targetFiles.forEach(file => {
    const fullPath = path.join(config.frontendPath, file);
    if (fs.existsSync(fullPath)) {
      files.push(fullPath);
    } else {
      if (config.verbose) {
        log(`文件不存在: ${file}`, 'warning');
      }
    }
  });

  // 递归查找其他包含模拟数据的文件
  function findDataFiles(dir) {
    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true });

      entries.forEach(entry => {
        const fullPath = path.join(dir, entry.name);

        if (entry.isDirectory() && !entry.name.startsWith('.') && !entry.name.includes('node_modules')) {
          findDataFiles(fullPath);
        } else if (entry.isFile() && (
          entry.name.includes('mock') ||
          entry.name.includes('Mock') ||
          entry.name.includes('test') ||
          entry.name.includes('Test') ||
          entry.name.includes('demo') ||
          entry.name.includes('Demo')
        ) && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
          if (!files.includes(fullPath)) {
            files.push(fullPath);
          }
        }
      });
    } catch (error) {
      // 忽略权限错误
    }
  }

  findDataFiles(config.frontendPath);
  return [...new Set(files)]; // 去重
}

// 主函数
async function main() {
  console.log(`
╔══════════════════════════════════════════════════════════════╗
║                  Newzora Mock Data Reset                    ║
║                     Version 1.0.0                          ║
╚══════════════════════════════════════════════════════════════╝
  `);

  log('开始重置Mock数据互动指标...', 'info');
  
  if (config.dryRun) {
    log('运行模式: DRY RUN (仅显示将要修改的内容)', 'warning');
  }

  // 创建备份
  if (!config.dryRun) {
    createBackup();
  }

  // 查找并处理文件
  const filesToProcess = findFilesToProcess();
  log(`找到 ${filesToProcess.length} 个文件需要处理`, 'info');

  let totalChanges = 0;
  let processedFiles = 0;

  for (const file of filesToProcess) {
    const changes = processFile(file);
    if (changes > 0) {
      totalChanges += changes;
      processedFiles++;
    }
  }

  // 输出结果
  console.log(`
╔══════════════════════════════════════════════════════════════╗
║                        重置完成!                            ║
╠══════════════════════════════════════════════════════════════╣
║ 处理文件: ${processedFiles.toString().padLeft(10)}                                    ║
║ 重置指标: ${totalChanges.toString().padLeft(10)}                                    ║
║ 模式: ${(config.dryRun ? 'DRY RUN' : 'PRODUCTION').padLeft(13)}                                 ║
╚══════════════════════════════════════════════════════════════╝
  `);

  if (config.dryRun) {
    log('这是 DRY RUN 模式，没有实际修改任何文件', 'warning');
    log('要执行实际重置，请运行: node scripts/reset-mock-data.js', 'info');
  } else {
    log('Mock数据互动指标重置完成!', 'success');
    log(`备份文件保存在: ${config.backupPath}`, 'info');
  }

  // 下一步提示
  console.log(`
🚀 下一步建议:
   1. 检查重置结果: git diff
   2. 运行测试: npm test
   3. 启动开发服务器: npm run dev
   4. 验证数据显示正确
  `);
}

// 添加字符串填充方法
String.prototype.padLeft = function(length, char = ' ') {
  return char.repeat(Math.max(0, length - this.length)) + this;
};

// 错误处理
process.on('uncaughtException', (error) => {
  log(`未捕获的异常: ${error.message}`, 'error');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log(`未处理的Promise拒绝: ${reason}`, 'error');
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    log(`脚本执行失败: ${error.message}`, 'error');
    process.exit(1);
  });
}

module.exports = { resetInteractionMetrics, processFile };
