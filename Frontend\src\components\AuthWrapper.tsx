'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';

interface AuthWrapperProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

/**
 * 统一的认证包装器 - 解决循环刷新问题
 */
export default function AuthWrapper({ 
  children, 
  requireAuth = false, 
  redirectTo = '/auth/login'
}: AuthWrapperProps) {
  const { isAuthenticated, isLoading, error } = useSimpleAuth();
  const router = useRouter();
  const [isInitialized, setIsInitialized] = useState(false);
  const [shouldRedirect, setShouldRedirect] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    // 处理错误信息
    if (error) {
      setErrorMessage(error);
    } else {
      setErrorMessage('');
    }
  }, [error]);

  useEffect(() => {
    // 等待认证状态加载完成
    if (!isLoading) {
      // 使用延迟确保状态稳定
      const timer = setTimeout(() => {
        setIsInitialized(true);
        
        // 检查是否需要重定向
        if (requireAuth && !isAuthenticated) {
          console.log('🔒 需要认证，准备重定向到登录页');
          setShouldRedirect(true);
        } else if (!requireAuth && isAuthenticated &&
                   (window.location.pathname === '/auth/login' ||
                    window.location.pathname === '/auth/register')) {
          console.log('✅ 用户已登录，准备重定向到首页');
          setShouldRedirect(true);
        }
      }, 300); // 300ms延迟确保状态稳定

      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, isLoading, requireAuth]);

  // 执行重定向
  useEffect(() => {
    if (shouldRedirect && isInitialized) {
      const targetUrl = requireAuth && !isAuthenticated ? redirectTo : '/';
      console.log(`🔄 执行重定向到: ${targetUrl}`);
      
      // 使用replace避免历史记录堆积
      router.replace(targetUrl);
    }
  }, [shouldRedirect, isInitialized, requireAuth, isAuthenticated, redirectTo, router]);

  // 显示加载状态
  if (isLoading || !isInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            {isLoading ? '正在验证登录状态...' : '正在初始化...'}
          </p>
        </div>
      </div>
    );
  }

  // 如果需要重定向，显示重定向状态
  if (shouldRedirect) {
    const message = requireAuth && !isAuthenticated
      ? 'Redirecting to login page...'
      : 'Redirecting to homepage...';

    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">{message}</p>
          {errorMessage && (
            <p className="mt-2 text-red-600">{errorMessage}</p>
          )}
        </div>
      </div>
    );
  }

  // 如果需要认证但用户未登录，不显示内容
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  // 如果不需要认证但用户已登录且在登录页，不显示内容
  if (!requireAuth && isAuthenticated && typeof window !== 'undefined' &&
      (window.location.pathname === '/auth/login' ||
       window.location.pathname === '/auth/register')) {
    return null;
  }

  return <>{children}</>;
}
