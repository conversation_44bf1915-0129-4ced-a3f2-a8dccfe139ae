'use client';

import { useState, useEffect } from 'react';
import { 
  getDeviceInfo, 
  getOperatingSystem, 
  getBrowserType, 
  supportsBrowserTranslation, 
  shouldUseSystemTranslation,
  DeviceInfo 
} from '@/utils/deviceDetection';

export interface ExtendedDeviceInfo extends DeviceInfo {
  operatingSystem: string;
  browserType: string;
  supportsBrowserTranslation: boolean;
  shouldUseSystemTranslation: boolean;
}

/**
 * React Hook for device detection
 * 检测设备类型、操作系统、浏览器等信息
 * 遵循项目规则：TypeScript强类型、完整错误处理、客户端安全检查
 */
export function useDeviceDetection(): ExtendedDeviceInfo {
  const [deviceInfo, setDeviceInfo] = useState<ExtendedDeviceInfo>(() => {
    const baseInfo = getDeviceInfo();
    return {
      ...baseInfo,
      operatingSystem: getOperatingSystem(),
      browserType: getBrowserType(),
      supportsBrowserTranslation: supportsBrowserTranslation(),
      shouldUseSystemTranslation: shouldUseSystemTranslation()
    };
  });

  useEffect(() => {
    const updateDeviceInfo = () => {
      const baseInfo = getDeviceInfo();
      setDeviceInfo({
        ...baseInfo,
        operatingSystem: getOperatingSystem(),
        browserType: getBrowserType(),
        supportsBrowserTranslation: supportsBrowserTranslation(),
        shouldUseSystemTranslation: shouldUseSystemTranslation()
      });
    };

    // 监听窗口大小变化
    window.addEventListener('resize', updateDeviceInfo);
    window.addEventListener('orientationchange', updateDeviceInfo);

    return () => {
      window.removeEventListener('resize', updateDeviceInfo);
      window.removeEventListener('orientationchange', updateDeviceInfo);
    };
  }, []);

  return deviceInfo;
}

/**
 * 简化的设备类型检测Hook
 */
export function useDeviceType() {
  const deviceInfo = useDeviceDetection();
  
  return {
    isMobile: deviceInfo.isMobile,
    isTablet: deviceInfo.isTablet,
    isDesktop: deviceInfo.isDesktop,
    isTouchDevice: deviceInfo.isTouchDevice
  };
}

/**
 * 翻译策略检测Hook
 */
export function useTranslationStrategy() {
  const deviceInfo = useDeviceDetection();
  
  return {
    strategy: deviceInfo.supportsBrowserTranslation ? 'browser' : 
              deviceInfo.shouldUseSystemTranslation ? 'system' : 'none',
    supportsBrowserTranslation: deviceInfo.supportsBrowserTranslation,
    shouldUseSystemTranslation: deviceInfo.shouldUseSystemTranslation,
    operatingSystem: deviceInfo.operatingSystem,
    browserType: deviceInfo.browserType
  };
}
