'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import AnalyticsDashboard from '@/components/AnalyticsDashboard';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { analyticsService } from '@/services/analyticsService';
import { recommendationEngine, simulateUserBehaviors } from '@/services/recommendationService';
import AuthGuard from '@/components/AuthGuard';
import Link from 'next/link';

function AnalyticsContent() {
  const router = useRouter();
  const { isAuthenticated, user, isLoading } = useSimpleAuth();
  const [initialized, setInitialized] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'content' | 'audience' | 'revenue'>('overview');

  // 认证检查
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // 初始化分析数据
  useEffect(() => {
    if (isAuthenticated && !initialized) {
      setDataLoading(true);

      // 模拟数据加载延迟
      setTimeout(() => {
        // 模拟用户行为数据
        simulateUserBehaviors();

        // 模拟一些分析数据
        const mockBehaviors = [
          {
            userId: user?.id || 'current-user',
            workId: 1,
            action: 'view' as const,
            timestamp: new Date(),
            category: 'technology',
            tags: ['AI', 'Machine Learning'],
            duration: 120
          },
          {
            userId: user?.id || 'current-user',
            workId: 2,
            action: 'like' as const,
            timestamp: new Date(),
            category: 'lifestyle',
            tags: ['Health', 'Wellness']
          },
          {
            userId: user?.id || 'current-user',
            workId: 3,
            action: 'share' as const,
            timestamp: new Date(),
            category: 'education',
            tags: ['Learning', 'Skills']
          }
        ];

        mockBehaviors.forEach(behavior => {
          analyticsService.addUserBehavior(behavior);
          recommendationEngine.recordUserBehavior(behavior);
        });

        setInitialized(true);
        setDataLoading(false);
      }, 1000);
    }
  }, [isAuthenticated, user, initialized]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Analytics Dashboard</h2>
          <p className="text-gray-600 mb-6">Please log in to view your analytics</p>
          <Link
            href="/auth/login"
            className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Login Now
          </Link>
        </div>
      </div>
    );
  }

  if (dataLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">📊 Analytics Dashboard</h1>
          <p className="text-gray-600">Comprehensive insights into your content performance and audience engagement</p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100">Total Content</p>
                <p className="text-2xl font-bold">16</p>
              </div>
              <div className="text-3xl">📝</div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100">Total Views</p>
                <p className="text-2xl font-bold">45.2K</p>
              </div>
              <div className="text-3xl">👁️</div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100">Engagement Rate</p>
                <p className="text-2xl font-bold">8.4%</p>
              </div>
              <div className="text-3xl">💬</div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100">Followers</p>
                <p className="text-2xl font-bold">1.2K</p>
              </div>
              <div className="text-3xl">👥</div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button 
                onClick={() => setActiveTab('overview')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Overview
              </button>
              <button 
                onClick={() => setActiveTab('content')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'content'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Content Performance
              </button>
              <button 
                onClick={() => setActiveTab('audience')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'audience'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Audience Insights
              </button>
              <button 
                onClick={() => setActiveTab('revenue')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'revenue'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Revenue Analytics
              </button>
            </nav>
          </div>
        </div>

        {/* Main Analytics Dashboard */}
        <AnalyticsDashboard activeTab={activeTab} />

        {/* Additional Insights */}
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Content Trends */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">📈 Content Trends</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="w-3 h-3 bg-green-500 rounded-full"></span>
                  <span className="font-medium text-gray-900">Technology</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-600 font-medium">↗️ +15%</span>
                  <span className="text-sm text-gray-500">vs last week</span>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
                  <span className="font-medium text-gray-900">Lifestyle</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-blue-600 font-medium">↗️ +8%</span>
                  <span className="text-sm text-gray-500">vs last week</span>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="w-3 h-3 bg-yellow-500 rounded-full"></span>
                  <span className="font-medium text-gray-900">Education</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-yellow-600 font-medium">→ 0%</span>
                  <span className="text-sm text-gray-500">vs last week</span>
                </div>
              </div>
            </div>
          </div>

          {/* Audience Demographics */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">👥 Audience Demographics</h3>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">Age 25-34</span>
                  <span className="text-gray-900 font-medium">45%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45%' }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">Age 18-24</span>
                  <span className="text-gray-900 font-medium">30%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '30%' }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">Age 35-44</span>
                  <span className="text-gray-900 font-medium">20%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full" style={{ width: '20%' }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">Other</span>
                  <span className="text-gray-900 font-medium">5%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-orange-500 h-2 rounded-full" style={{ width: '5%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex flex-col sm:flex-row gap-4">
          <Link
            href="/earnings"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            💰 View Earnings
          </Link>
          <Link
            href="/content"
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            📝 Manage Content
          </Link>
          <button 
            onClick={() => {
              // 生成并下载报告
              const reportData = {
                date: new Date().toISOString(),
                totalViews: '45.2K',
                totalContent: 16,
                engagementRate: '8.4%',
                followers: '1.2K'
              };
              const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `analytics-report-${new Date().toISOString().split('T')[0]}.json`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            }}
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            📊 Export Report
          </button>
        </div>
      </div>
    </div>
  );
}

export default function AnalyticsPage() {
  return (
    <AuthGuard requireAuth={true} redirectTo="/auth/login">
      <AnalyticsContent />
    </AuthGuard>
  );
}
