'use client';

import React, { useState, useEffect } from 'react';

interface HydrationSafeWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

/**
 * HydrationSafeWrapper 组件
 * 防止服务端渲染和客户端渲染不匹配导致的水合错误
 * 遵循项目规则：TypeScript强类型、完整错误处理、用户体验优化
 */
export default function HydrationSafeWrapper({ 
  children, 
  fallback = null,
  className = ''
}: HydrationSafeWrapperProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保只在客户端设置为true
    setIsClient(true);
  }, []);

  // 服务端渲染时显示fallback或空内容
  if (!isClient) {
    return (
      <div className={className} suppressHydrationWarning>
        {fallback}
      </div>
    );
  }

  // 客户端渲染时显示实际内容
  return (
    <div className={className}>
      {children}
    </div>
  );
}

/**
 * Higher-order component version
 */
export function withHydrationSafe<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode
) {
  return function HydrationSafeComponent(props: P) {
    return (
      <HydrationSafeWrapper fallback={fallback}>
        <Component {...props} />
      </HydrationSafeWrapper>
    );
  };
}

/**
 * Hook for client-side only operations
 */
export function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}
