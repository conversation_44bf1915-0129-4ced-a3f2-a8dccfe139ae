'use client';

import React, { useEffect, useState, createContext, useContext } from 'react';
import { translationManager, TranslationConfig } from '@/utils/translationManager';
import { getTranslationStrategy, isDesktopDevice, isMobileDevice } from '@/utils/deviceDetection';

interface TranslationContextType {
  config: TranslationConfig;
  isInitialized: boolean;
  strategy: 'browser' | 'system' | 'none';
  showTranslationTip: boolean;
  dismissTranslationTip: () => void;
}

const TranslationContext = createContext<TranslationContextType | null>(null);

interface TranslationProviderProps {
  children: React.ReactNode;
}

/**
 * 翻译提供者组件
 * 管理PC端和移动端的不同翻译策略
 * 遵循项目规则：TypeScript强类型、完整错误处理、用户体验优化
 */
export default function TranslationProvider({ children }: TranslationProviderProps) {
  const [config, setConfig] = useState<TranslationConfig>({
    strategy: 'none',
    enabled: false,
    autoDetect: true,
    defaultLanguage: 'en',
    supportedLanguages: []
  });
  const [isInitialized, setIsInitialized] = useState(false);
  const [showTranslationTip, setShowTranslationTip] = useState(false);

  useEffect(() => {
    const initializeTranslation = async () => {
      try {
        await translationManager.initialize();
        const newConfig = translationManager.getConfig();
        setConfig(newConfig);
        setIsInitialized(true);

        // 检查是否需要显示翻译提示
        if (newConfig.enabled && typeof window !== 'undefined') {
          const userLanguage = navigator.language || 'en';
          if (!userLanguage.startsWith('en')) {
            setShowTranslationTip(true);
          }
        }
      } catch (error) {
        console.error('Failed to initialize translation:', error);
      }
    };

    initializeTranslation();

    // 清理函数
    return () => {
      if (translationManager.isInitialized()) {
        translationManager.cleanup();
      }
    };
  }, []);

  const dismissTranslationTip = () => {
    setShowTranslationTip(false);
    // 保存用户偏好
    if (typeof window !== 'undefined') {
      localStorage.setItem('translation_tip_dismissed', 'true');
    }
  };

  // 检查用户是否已经关闭过翻译提示
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const dismissed = localStorage.getItem('translation_tip_dismissed');
      if (dismissed === 'true') {
        setShowTranslationTip(false);
      }
    }
  }, []);

  const contextValue: TranslationContextType = {
    config,
    isInitialized,
    strategy: config.strategy,
    showTranslationTip,
    dismissTranslationTip
  };

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
      {showTranslationTip && <TranslationTip />}
    </TranslationContext.Provider>
  );
}

/**
 * 翻译提示组件
 */
function TranslationTip() {
  const context = useContext(TranslationContext);
  if (!context) return null;

  const { strategy, dismissTranslationTip } = context;
  const isDesktop = isDesktopDevice();
  const isMobile = isMobileDevice();

  const getTipContent = () => {
    if (strategy === 'browser' && isDesktop) {
      return {
        title: 'Translation Available',
        message: 'Right-click on this page and select "Translate" to view content in your language.',
        icon: '🌐'
      };
    } else if (strategy === 'system' && isMobile) {
      return {
        title: 'Translation Available',
        message: 'Use your device\'s built-in translation features to translate this page.',
        icon: '📱'
      };
    }
    return null;
  };

  const tipContent = getTipContent();
  if (!tipContent) return null;

  return (
    <div className={`translation-tip ${isMobile ? 'mobile' : ''}`}>
      <div className="flex items-start space-x-3">
        <span className="text-xl">{tipContent.icon}</span>
        <div className="flex-1">
          <h4 className="font-medium text-gray-900 mb-1">{tipContent.title}</h4>
          <p className="text-sm text-gray-600 mb-3">{tipContent.message}</p>
          <div className="flex space-x-2">
            <button
              onClick={dismissTranslationTip}
              className="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors"
            >
              Got it
            </button>
            <button
              onClick={dismissTranslationTip}
              className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * 使用翻译上下文的Hook
 */
export function useTranslation() {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  return context;
}

/**
 * 翻译状态指示器组件
 */
export function TranslationStatus() {
  const { strategy, isInitialized } = useTranslation();

  if (!isInitialized) return null;

  const getStatusInfo = () => {
    switch (strategy) {
      case 'browser':
        return {
          text: 'Browser Translation Available',
          color: 'text-green-600',
          icon: '🌐'
        };
      case 'system':
        return {
          text: 'System Translation Available',
          color: 'text-blue-600',
          icon: '📱'
        };
      default:
        return {
          text: 'Translation Not Available',
          color: 'text-gray-500',
          icon: '❌'
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className="flex items-center space-x-2 text-xs">
      <span>{statusInfo.icon}</span>
      <span className={statusInfo.color}>{statusInfo.text}</span>
    </div>
  );
}
